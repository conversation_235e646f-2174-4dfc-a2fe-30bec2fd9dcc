# AI Agent Development Guide for Twilio Gemini Live API Project

## Project Context for AI Agents
This document provides essential information for AI coding assistants working on the Twilio Gemini Live API project. It outlines directory structures, conventions, and best practices specific to this codebase.

## Working Directory Context
- **Primary workspace**: `/home/<USER>/github/verduona-full/twilio-gemini-liveapi`
- **Repository root**: `/home/<USER>/github/verduona-full`
- **Project type**: Node.js voice AI call center system with TypeScript/JavaScript
- **Main technologies**: Twilio, Google Gemini Live API, Fastify, Next.js, WebSockets

## Directory Structure for AI Agents

### Source Code Organization (`src/`)
When working on core functionality, organize code by domain:

```
src/
├── api/                    # REST API endpoints
├── audio/                  # Audio processing and streaming
├── config/                 # Configuration management
├── context/                # Conversation context handling
├── gemini/                 # Google Gemini AI integration
├── middleware/             # Express/Fastify middleware
├── scripts/                # Campaign script management
├── session/                # Session lifecycle management
├── types/                  # TypeScript type definitions
├── utils/                  # Utility functions and helpers
└── websocket/              # WebSocket message handling
```

### Testing Directory (`test/`)
When adding tests, follow this structure:

```
test/
├── helpers/                # Shared test utilities
│   └── env.js             # Environment setup for tests
├── [component].test.js    # Component-specific tests
└── integration/           # End-to-end integration tests
```

### Frontend Directory (`call-center-frontend/`)
For frontend development:

```
call-center-frontend/
├── app/                   # Next.js 13+ app router
├── lib/                   # Frontend utilities
├── public/                # Static assets
└── components/            # React components
```

## File Naming and Location Guidelines

### Where to Place New Files

#### API Development
- **Main endpoints**: `src/api/routes.ts`
- **Management features**: `src/api/management.ts`
- **Testing endpoints**: `src/api/testing.ts`

#### Audio Processing
- **Core processing**: `src/audio/audio-processor.ts`
- **Stream forwarding**: `src/audio/audio-forwarding.ts`
- **Transcription**: `src/audio/transcription-manager.ts`

#### Configuration
- **Main config**: `src/config/config.ts`
- **Audio settings**: `src/config/audio-config.ts`
- **Business logic**: `src/config/business-config.ts`
- **Campaign settings**: `src/config/campaign-config.ts`

#### Session Management
- **Main orchestrator**: `src/session/session-manager.ts`
- **Lifecycle events**: `src/session/lifecycle-manager.ts`
- **Error recovery**: `src/session/recovery-manager.ts`
- **Health monitoring**: `src/session/health-monitor.ts`

#### WebSocket Handling
- **Message routing**: `src/websocket/handlers.ts`
- **Audio streams**: `src/websocket/audio-handlers.ts`
- **Session events**: `src/websocket/session-events.ts`
- **Twilio flows**: `src/websocket/twilio-flow-handler.ts`
- **Testing flows**: `src/websocket/local-testing-handler.ts`

#### Utilities
- **General utilities**: `src/utils/`
- **Logging**: `src/utils/logger.js`
- **Test helpers**: `test/helpers/`

### Testing File Placement

#### Test File Naming
- **Pattern**: `[component-name].test.js`
- **Location**: `test/` directory (root level)
- **Examples**:
  - `test/session-manager.test.js`
  - `test/audio-processor.test.js`
  - `test/configuration.test.js`

#### Test Categories
- **Unit tests**: Individual component testing
- **Integration tests**: Multi-component workflow testing
- **API tests**: Endpoint validation
- **Flow tests**: Complete user journey testing

## Development Conventions for AI Agents

### File Extensions and Languages
- **TypeScript**: `.ts` for new modules (preferred)
- **JavaScript**: `.js` for legacy modules (being migrated)
- **Tests**: `.test.js` using Node.js built-in test runner

### Naming Conventions
- **Files**: kebab-case (`audio-processor.ts`)
- **Functions**: camelCase (`processAudio()`)
- **Classes**: PascalCase (`AudioProcessor`)
- **Constants**: UPPER_CASE (`MAX_RETRIES`)

### Import/Export Patterns
```javascript
// External packages first
import { WebSocket } from 'ws';
import { FastifyInstance } from 'fastify';

// Internal modules second
import { SessionManager } from '../session/session-manager.js';
import { AudioProcessor } from '../audio/audio-processor.js';

// Relative imports last
import { handleAudioData } from './audio-handlers.js';
```

### Logging Standards
Always use the structured logger from `src/utils/logger.js`:

```javascript
import { logger } from '../utils/logger.js';

// Good - with emoji prefix and context
logger.info('🎤 Audio processed successfully', { 
    sessionId, 
    audioSize: buffer.length 
});

// Bad - don't use console.log
console.log('Audio processed');
```

### Error Handling Patterns
```javascript
try {
    await processAudio(audioData);
} catch (error) {
    logger.error('❌ Audio processing failed', { 
        sessionId, 
        error: error.message,
        stack: error.stack 
    });
    // Implement recovery logic
}
```

## Testing Guidelines for AI Agents

### Test Structure
Use Node.js built-in test runner:

```javascript
import { test, describe } from 'node:test';
import assert from 'node:assert';

describe('AudioProcessor', () => {
    test('should convert μ-law to PCM', async () => {
        const processor = new AudioProcessor();
        const result = await processor.convertUlawToPCM(ulawData);
        assert.ok(result instanceof Buffer);
    });
});
```

### Test Requirements
- **All 27+ tests must pass** before committing
- Test all 4 flows: outbound/inbound × Twilio/browser
- Include both unit and integration tests
- Use `test/helpers/` for shared utilities

### Running Tests
```bash
npm test                    # Run all tests
npm run test:workflow      # Run workflow integration tests
npm run test:4-flows       # Test all 4 major flows
```

## Code Quality Standards

### Before Committing
1. `npm run lint:fix` - Fix linting issues
2. `npm run type-check` - Verify TypeScript types
3. `npm test` - All tests must pass
4. Manual testing of affected flows

### File Size Guidelines
- **Maximum 300 lines per file**
- Split large files into focused modules
- Extract reusable functions to `src/utils/`
- Separate concerns clearly

### Architecture Principles
- **Single responsibility** per module
- **Dependency injection** where appropriate
- **Error recovery** mechanisms
- **Structured logging** with context

## Common Development Patterns

### Adding New API Endpoint
1. Define route in `src/api/routes.ts`
2. Implement handler in appropriate API file
3. Add input validation
4. Create corresponding test in `test/backend-api.test.js`

### Adding WebSocket Handler
1. Add message type to `src/websocket/handlers.ts`
2. Implement handler in appropriate file
3. Add session state management
4. Test with all 4 flows

### Adding Configuration Option
1. Add to `src/config/config.ts`
2. Update environment validation
3. Document in `.clinerules`
4. Add test in `test/configuration.test.js`

## Performance and Monitoring

### Session Management
- Clean up inactive sessions
- Monitor memory usage
- Implement connection pooling
- Use caching appropriately

### Audio Processing
- Handle real-time constraints
- Implement buffering strategies
- Monitor processing latency
- Graceful degradation on errors

## Security Considerations

### Input Validation
- Use `src/middleware/security-utils.js`
- Validate all user inputs
- Sanitize phone numbers and text
- Implement rate limiting

### Authentication
- Use proper authentication middleware
- Validate Twilio webhook signatures
- Secure API endpoints
- Never log sensitive data

This guide provides AI agents with the essential context needed to work effectively on the Twilio Gemini Live API project while maintaining code quality and architectural consistency.
