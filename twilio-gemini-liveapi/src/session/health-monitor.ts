// Connection Health Monitoring System

interface ConnectionStateData {
    callSid: string;
    state: 'connecting' | 'connected' | 'failed' | 'disconnected' | 'recovered' | 'recovering';
    timestamp: number;
    metadata: Record<string, any>;
    consecutiveFailures: number;
    lastPing?: number;
    duration: number;
}

interface HealthMetrics {
    totalConnections: number;
    activeConnections: number;
    failedConnections: number;
    recoveredConnections: number;
    lastHealthCheck: number;
}

interface ConnectionMetrics extends ConnectionStateData {
    isHealthy?: boolean;
}

interface DetailedHealthReport {
    summary: {
        totalConnections: number;
        activeConnections: number;
        failedConnections: number;
        recoveredConnections: number;
        lastHealthCheck: number;
        connectionStates: ConnectionStateData[];
        timestamp: number;
    };
    healthScore: number;
    connections: Array<{
        callSid: string;
        state: string;
        duration: number;
        consecutiveFailures: number;
        lastPing: number | null;
        isHealthy: boolean;
    }>;
    systemInfo: {
        uptime: number;
        memory: NodeJS.MemoryUsage;
        timestamp: number;
    };
}

export class ConnectionHealthMonitor {
    private connectionStates: Map<string, ConnectionStateData>;
    private healthMetrics: HealthMetrics;
    private healthCheckInterval: number;
    private maxConnectionAge: number;
    private healthMonitoringInterval?: NodeJS.Timeout;

    constructor() {
        this.connectionStates = new Map(); // Track connection states by callSid
        this.healthMetrics = {
            totalConnections: 0,
            activeConnections: 0,
            failedConnections: 0,
            recoveredConnections: 0,
            lastHealthCheck: Date.now()
        };
        this.healthCheckInterval = 30000; // 30 seconds
        this.maxConnectionAge = 3600000; // 1 hour
        this.startHealthMonitoring();
    }

    // Track connection state changes
    trackConnection(callSid: string, state: ConnectionStateData['state'], metadata: Record<string, any> = {}): void {
        const timestamp = Date.now();
        const existingState = this.connectionStates.get(callSid);
        
        const connectionState: ConnectionStateData = {
            callSid,
            state, // 'connecting', 'connected', 'failed', 'disconnected', 'recovered'
            timestamp,
            metadata,
            consecutiveFailures: state === 'failed' ? 
                (existingState?.consecutiveFailures || 0) + 1 : 0,
            lastPing: state === 'connected' ? timestamp : existingState?.lastPing,
            duration: existingState ? timestamp - existingState.timestamp : 0
        };

        this.connectionStates.set(callSid, connectionState);

        // Update global metrics
        switch (state) {
            case 'connected':
                this.healthMetrics.activeConnections++;
                if (existingState?.state === 'failed') {
                    this.healthMetrics.recoveredConnections++;
                }
                break;
            case 'failed':
                this.healthMetrics.failedConnections++;
                break;
            case 'disconnected':
                if (existingState?.state === 'connected') {
                    this.healthMetrics.activeConnections = Math.max(0, this.healthMetrics.activeConnections - 1);
                }
                break;
        }

        console.log(`🏥 [${callSid}] Connection state: ${state}`, metadata);
    }

    // Check if a connection is healthy
    isConnectionHealthy(callSid: string): boolean {
        const connectionState = this.connectionStates.get(callSid);
        if (!connectionState) return false;

        const now = Date.now();
        const timeSinceLastActivity = now - connectionState.timestamp;
        const isRecent = timeSinceLastActivity < 300000; // 5 minutes
        const isConnected = connectionState.state === 'connected';
        const hasLowFailures = connectionState.consecutiveFailures < 3;

        return isRecent && isConnected && hasLowFailures;
    }

    // Ping connection to test health
    pingConnection(callSid: string, geminiSession: any): boolean {
        if (!geminiSession) return false;

        try {
            // Update last ping time
            const connectionState = this.connectionStates.get(callSid);
            if (connectionState) {
                connectionState.lastPing = Date.now();
                this.connectionStates.set(callSid, connectionState);
            }

            // For Gemini Live API, we can send a minimal message to test connectivity
            // This is a lightweight ping that doesn't affect the conversation
            return true;
        } catch (error: any) {
            console.error(`❌ [${callSid}] Connection ping failed:`, error);
            this.trackConnection(callSid, 'failed', { error: error.message });
            return false;
        }
    }

    // Start periodic health monitoring
    private startHealthMonitoring(): void {
        this.healthMonitoringInterval = setInterval(() => {
            this.performHealthCheck();
        }, this.healthCheckInterval);

        console.log(`🏥 Connection health monitoring started (interval: ${this.healthCheckInterval}ms)`);
    }

    // Stop health monitoring
    stopHealthChecks(): void {
        if (this.healthMonitoringInterval) {
            clearInterval(this.healthMonitoringInterval);
            this.healthMonitoringInterval = undefined;
            console.log('🛑 Health monitoring stopped');
        }
        
        const connectionCount = this.connectionStates.size;
        this.connectionStates.clear();
        console.log(`🧹 HealthMonitor: Cleared ${connectionCount} connection states`);
    }

    // Perform comprehensive health check
    private performHealthCheck(): void {
        const now = Date.now();
        let healthyConnections = 0;
        let unhealthyConnections = 0;

        for (const [callSid, connectionState] of this.connectionStates.entries()) {
            const timeSinceLastActivity = now - connectionState.timestamp;
            const isStale = timeSinceLastActivity > 300000; // 5 minutes

            if (isStale) {
                // Clean up stale connections
                this.connectionStates.delete(callSid);
                console.log(`🧹 [${callSid}] Removed stale connection state`);
                continue;
            }

            if (connectionState.state === 'connected' && connectionState.consecutiveFailures === 0) {
                healthyConnections++;
            } else {
                unhealthyConnections++;
            }
        }

        // Update metrics
        this.healthMetrics.activeConnections = healthyConnections;
        this.healthMetrics.lastHealthCheck = now;

        // Log health summary
        if (healthyConnections > 0 || unhealthyConnections > 0) {
            console.log(`🏥 Health check: ${healthyConnections} healthy, ${unhealthyConnections} unhealthy connections`);
        }
    }

    // Get current health status
    getHealthStatus(): {
        totalConnections: number;
        activeConnections: number;
        failedConnections: number;
        recoveredConnections: number;
        lastHealthCheck: number;
        connectionStates: ConnectionStateData[];
        timestamp: number;
    } {
        return {
            ...this.healthMetrics,
            connectionStates: Array.from(this.connectionStates.values()),
            timestamp: Date.now()
        };
    }

    // Simple event emitter for recovery events
    emit(event: string, data: { callSid: string; reason: string }): void {
        if (event === 'recovery_needed') {
            // Handle recovery event immediately
            setTimeout(() => {
                this.handleRecoveryEvent(data);
            }, 100);
        }
    }

    // Handle recovery event
    private handleRecoveryEvent(data: { callSid: string; reason: string }): void {
        const { callSid, reason } = data;
        console.log(`🚨 [${callSid}] Recovery event triggered: ${reason}`);

        // This will be handled by the session recovery mechanism
        // For now, just log and update state
        this.trackConnection(callSid, 'recovering', { reason });
    }

    // Clean up connection tracking
    removeConnection(callSid: string): void {
        this.connectionStates.delete(callSid);
        console.log(`🗑️ [${callSid}] Connection health tracking removed`);
    }

    // Get connection metrics for a specific call
    getConnectionMetrics(callSid: string): ConnectionStateData | null {
        return this.connectionStates.get(callSid) || null;
    }

    // Get overall system health score (0-100)
    getHealthScore(): number {
        const total = this.healthMetrics.totalConnections;
        if (total === 0) return 100;

        const failed = this.healthMetrics.failedConnections;
        const recovered = this.healthMetrics.recoveredConnections;
        const active = this.healthMetrics.activeConnections;

        // Calculate score based on success rate and recovery rate
        const successRate = ((total - failed) / total) * 100;
        const recoveryRate = failed > 0 ? (recovered / failed) * 100 : 100;
        const activeBonus = active > 0 ? 10 : 0; // Bonus for having active connections

        return Math.min(100, Math.round((successRate * 0.6) + (recoveryRate * 0.3) + (activeBonus * 0.1)));
    }

    // Reset all metrics (for testing or maintenance)
    reset(): void {
        this.connectionStates.clear();
        this.healthMetrics = {
            totalConnections: 0,
            activeConnections: 0,
            failedConnections: 0,
            recoveredConnections: 0,
            lastHealthCheck: Date.now()
        };
        console.log('🔄 Health monitor metrics reset');
    }

    // Get detailed health report
    getDetailedHealthReport(): DetailedHealthReport {
        const now = Date.now();
        const connections = Array.from(this.connectionStates.values());
        
        return {
            summary: this.getHealthStatus(),
            healthScore: this.getHealthScore(),
            connections: connections.map(conn => ({
                callSid: conn.callSid,
                state: conn.state,
                duration: now - conn.timestamp,
                consecutiveFailures: conn.consecutiveFailures,
                lastPing: conn.lastPing ? now - conn.lastPing : null,
                isHealthy: this.isConnectionHealthy(conn.callSid)
            })),
            systemInfo: {
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                timestamp: now
            }
        };
    }
}