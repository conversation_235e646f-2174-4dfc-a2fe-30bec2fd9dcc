// Deepgram transcription helper
import { createClient, LiveClient, LiveTranscriptionEvents } from '@deepgram/sdk';

// Type definitions
interface TranscriptionData {
    transcript: string;
    confidence: number;
    timestamp: number;
    isFinal: boolean;
}

interface TranscriptEntry {
    role: string;
    content: string;
    timestamp: number;
    confidence: number;
    source?: string;
}

interface ConnectionData {
    fullTranscript?: TranscriptEntry[];
    conversationLog?: TranscriptEntry[];
}

interface TranscriptionConnectionData {
    connection: LiveClient;
    handlers: {
        open?: () => void;
        transcript?: (data: any) => void;
        error?: (error: any) => void;
        close?: () => void;
    };
}

interface TranscriptionStatus {
    isActive: boolean;
    connectionState: string;
    timestamp: number;
}

interface TranscriptionStats {
    activeConnections: number;
    deepgramAvailable: boolean;
    callSids: string[];
    timestamp: number;
}

interface HealthCheckResult {
    status: string;
    reason?: string;
    activeConnections?: number;
    error?: string;
    timestamp: number;
}

export class TranscriptionManager {
    private deepgramClient: any;
    private activeConnections: Map<string, TranscriptionConnectionData | LiveClient>;

    constructor() {
        this.deepgramClient = process.env.DEEPGRAM_API_KEY ? createClient(process.env.DEEPGRAM_API_KEY) : null;
        this.activeConnections = new Map(); // Track active transcription connections
    }

    static async initializeTranscription(callSid: string, connectionData: ConnectionData): Promise<LiveClient | null> {
        const manager = new TranscriptionManager();
        return await manager.createTranscription(callSid, connectionData);
    }

    async createTranscription(callSid: string, connectionData: ConnectionData): Promise<LiveClient | null> {
        if (!this.deepgramClient) {
            console.warn(`⚠️ [${callSid}] Deepgram not available - transcription disabled`);
            return null;
        }

        if (!connectionData) {
            console.warn(`⚠️ [${callSid}] No connection data - transcription disabled`);
            return null;
        }

        try {
            const dgConnection = this.deepgramClient.listen.live({
                model: 'nova-2',
                language: 'en',
                smart_format: true,
                interim_results: true,
                utterance_end_ms: 1000,
                vad_events: true,
                encoding: 'mulaw',
                sample_rate: 8000,
                channels: 1
            });

            // Store event handlers for cleanup
            const eventHandlers: TranscriptionConnectionData['handlers'] = {};
            
            eventHandlers.open = () => {
                console.log(`🎤 [${callSid}] Deepgram transcription connection opened`);
                this.activeConnections.set(callSid, { connection: dgConnection, handlers: eventHandlers });
            };
            dgConnection.on(LiveTranscriptionEvents.Open, eventHandlers.open);

            eventHandlers.transcript = (data: any) => {
                try {
                    const transcript = data.channel?.alternatives?.[0]?.transcript;
                    const confidence = data.channel?.alternatives?.[0]?.confidence || 0.9;
                    
                    if (transcript && data.is_final) {
                        console.log(`📝 [${callSid}] User said: "${transcript}"`);

                        // Add to full transcript log with bounds checking
                        if (connectionData && connectionData.fullTranscript) {
                            const MAX_TRANSCRIPT_SIZE = 1000;
                            connectionData.fullTranscript.push({
                                role: 'user',
                                content: transcript,
                                timestamp: Date.now(),
                                confidence: confidence
                            });
                            
                            // Trim if exceeds limit
                            if (connectionData.fullTranscript.length > MAX_TRANSCRIPT_SIZE) {
                                connectionData.fullTranscript.splice(0, connectionData.fullTranscript.length - MAX_TRANSCRIPT_SIZE);
                            }
                        }

                        // Add to conversation log with bounds checking
                        if (connectionData && connectionData.conversationLog) {
                            const MAX_CONVERSATION_SIZE = 500;
                            connectionData.conversationLog.push({
                                role: 'user',
                                content: transcript,
                                timestamp: Date.now(),
                                source: 'deepgram',
                                confidence: confidence
                            });
                            
                            // Trim if exceeds limit
                            if (connectionData.conversationLog.length > MAX_CONVERSATION_SIZE) {
                                connectionData.conversationLog.splice(0, connectionData.conversationLog.length - MAX_CONVERSATION_SIZE);
                            }
                        }

                        // Emit transcription event for other systems
                        this.emitTranscriptionEvent(callSid, {
                            transcript,
                            confidence: confidence,
                            timestamp: Date.now(),
                            isFinal: data.is_final
                        });
                    }
                } catch (error) {
                    console.warn(`⚠️ [${callSid}] Deepgram transcript processing error:`, error);
                }
            };
            dgConnection.on(LiveTranscriptionEvents.Transcript, eventHandlers.transcript);

            eventHandlers.error = (error: any) => {
                console.error(`❌ [${callSid}] Deepgram transcription error:`, error);
                this.closeTranscription(callSid);
            };
            dgConnection.on(LiveTranscriptionEvents.Error, eventHandlers.error);

            eventHandlers.close = () => {
                console.log(`🔌 [${callSid}] Deepgram transcription connection closed`);
                this.activeConnections.delete(callSid);
            };
            dgConnection.on(LiveTranscriptionEvents.Close, eventHandlers.close);

            return dgConnection;
        } catch (error) {
            console.error(`❌ [${callSid}] Failed to initialize Deepgram transcription:`, error);
            return null;
        }
    }

    static closeTranscription(callSid: string, dgConnection: LiveClient): void {
        if (dgConnection && typeof dgConnection.finish === 'function') {
            try {
                dgConnection.finish();
                console.log(`🔌 [${callSid}] Deepgram transcription closed`);
            } catch (error) {
                console.warn(`⚠️ [${callSid}] Error closing Deepgram transcription:`, error);
            }
        }
    }

    // Send audio data to transcription service
    sendAudioToTranscription(callSid: string, audioBuffer: Buffer): void {
        const connectionData = this.activeConnections.get(callSid);
        const dgConnection = (connectionData as TranscriptionConnectionData)?.connection || connectionData as LiveClient;
        if (dgConnection && audioBuffer) {
            try {
                dgConnection.send(audioBuffer);
            } catch (error) {
                console.error(`❌ [${callSid}] Error sending audio to transcription:`, error);
            }
        }
    }

    // Get transcription status
    getTranscriptionStatus(callSid: string): TranscriptionStatus {
        const dgConnection = this.activeConnections.get(callSid);
        return {
            isActive: !!dgConnection,
            connectionState: dgConnection ? 'connected' : 'disconnected',
            timestamp: Date.now()
        };
    }

    // Close transcription for a specific call
    closeTranscription(callSid: string): void {
        const connectionData = this.activeConnections.get(callSid);
        if (connectionData) {
            // Remove all event listeners first
            if ('handlers' in connectionData && connectionData.handlers && connectionData.connection) {
                const conn = connectionData.connection;
                const handlers = connectionData.handlers;
                
                // Remove all registered event listeners
                if (handlers.open) conn.removeListener(LiveTranscriptionEvents.Open, handlers.open);
                if (handlers.transcript) conn.removeListener(LiveTranscriptionEvents.Transcript, handlers.transcript);
                if (handlers.error) conn.removeListener(LiveTranscriptionEvents.Error, handlers.error);
                if (handlers.close) conn.removeListener(LiveTranscriptionEvents.Close, handlers.close);
            }
            
            // Close the connection
            const connection = 'connection' in connectionData ? connectionData.connection : connectionData as LiveClient;
            TranscriptionManager.closeTranscription(callSid, connection);
            this.activeConnections.delete(callSid);
        }
    }

    // Close all active transcriptions
    closeAllTranscriptions(): void {
        for (const [callSid, connectionData] of this.activeConnections.entries()) {
            const connection = 'connection' in connectionData ? connectionData.connection : connectionData as LiveClient;
            TranscriptionManager.closeTranscription(callSid, connection);
        }
        this.activeConnections.clear();
        console.log('🔌 All transcription connections closed');
    }

    // Clean up all transcription resources (for shutdown)
    cleanup(): void {
        const connectionCount = this.activeConnections.size;
        this.closeAllTranscriptions();
        console.log(`🧹 TranscriptionManager: Cleaned up ${connectionCount} active transcription connections`);
    }

    // Get active transcription count
    getActiveTranscriptionCount(): number {
        return this.activeConnections.size;
    }

    // Get all active transcription call SIDs
    getActiveTranscriptionCallSids(): string[] {
        return Array.from(this.activeConnections.keys());
    }

    // Emit transcription event (can be extended for event system)
    private emitTranscriptionEvent(callSid: string, transcriptionData: TranscriptionData): void {
        // This could be extended to emit events to an event system
        // For now, just log debug info
        if (process.env.TRANSCRIPTION_DEBUG === 'true') {
            console.log(`📝 [${callSid}] Transcription event:`, {
                transcript: transcriptionData.transcript.substring(0, 50) + '...',
                confidence: transcriptionData.confidence,
                timestamp: new Date(transcriptionData.timestamp).toISOString()
            });
        }
    }

    // Get transcription statistics
    getTranscriptionStats(): TranscriptionStats {
        return {
            activeConnections: this.activeConnections.size,
            deepgramAvailable: !!this.deepgramClient,
            callSids: Array.from(this.activeConnections.keys()),
            timestamp: Date.now()
        };
    }

    // Health check for transcription service
    async healthCheck(): Promise<HealthCheckResult> {
        try {
            if (!this.deepgramClient) {
                return {
                    status: 'unavailable',
                    reason: 'Deepgram API key not configured',
                    timestamp: Date.now()
                };
            }

            // Basic health check - just verify client exists
            return {
                status: 'healthy',
                activeConnections: this.activeConnections.size,
                timestamp: Date.now()
            };
        } catch (error: any) {
            return {
                status: 'error',
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    // Initialize transcription for local audio session
    async initializeLocalTranscription(sessionId: string, connectionData: ConnectionData): Promise<LiveClient | null> {
        if (!this.deepgramClient) {
            console.warn(`⚠️ [${sessionId}] Deepgram not available for local transcription`);
            return null;
        }

        try {
            const dgConnection = this.deepgramClient.listen.live({
                model: 'nova-2',
                language: 'en',
                smart_format: true,
                interim_results: true,
                utterance_end_ms: 1000,
                vad_events: true,
                encoding: 'linear16', // Different encoding for local audio
                sample_rate: 16000,   // Different sample rate for local audio
                channels: 1
            });

            dgConnection.on(LiveTranscriptionEvents.Open, () => {
                console.log(`🎤 [${sessionId}] Local Deepgram transcription connection opened`);
                this.activeConnections.set(sessionId, dgConnection);
            });

            dgConnection.on(LiveTranscriptionEvents.Transcript, (data: any) => {
                try {
                    const transcript = data.channel?.alternatives?.[0]?.transcript;
                    if (transcript && data.is_final) {
                        console.log(`📝 [${sessionId}] Local user said: "${transcript}"`);

                        // Add to conversation data with bounds checking
                        if (connectionData) {
                            if (connectionData.fullTranscript) {
                                const MAX_TRANSCRIPT_SIZE = 1000;
                                connectionData.fullTranscript.push({
                                    role: 'user',
                                    content: transcript,
                                    timestamp: Date.now(),
                                    confidence: data.channel.alternatives[0].confidence || 0.9,
                                    source: 'local_deepgram'
                                });
                                
                                // Trim if exceeds limit
                                if (connectionData.fullTranscript.length > MAX_TRANSCRIPT_SIZE) {
                                    connectionData.fullTranscript.splice(0, connectionData.fullTranscript.length - MAX_TRANSCRIPT_SIZE);
                                }
                            }

                            if (connectionData.conversationLog) {
                                const MAX_CONVERSATION_SIZE = 500;
                                connectionData.conversationLog.push({
                                    role: 'user',
                                    content: transcript,
                                    timestamp: Date.now(),
                                    source: 'local_deepgram',
                                    confidence: data.channel.alternatives[0].confidence || 0.9
                                });
                                
                                // Trim if exceeds limit
                                if (connectionData.conversationLog.length > MAX_CONVERSATION_SIZE) {
                                    connectionData.conversationLog.splice(0, connectionData.conversationLog.length - MAX_CONVERSATION_SIZE);
                                }
                            }
                        }
                    }
                } catch (error) {
                    console.warn(`⚠️ [${sessionId}] Local Deepgram transcript processing error:`, error);
                }
            });

            dgConnection.on(LiveTranscriptionEvents.Error, (error: any) => {
                console.error(`❌ [${sessionId}] Local Deepgram transcription error:`, error);
                this.activeConnections.delete(sessionId);
            });

            dgConnection.on(LiveTranscriptionEvents.Close, () => {
                console.log(`🔌 [${sessionId}] Local Deepgram transcription connection closed`);
                this.activeConnections.delete(sessionId);
            });

            return dgConnection;
        } catch (error) {
            console.error(`❌ [${sessionId}] Failed to initialize local Deepgram transcription:`, error);
            return null;
        }
    }
}