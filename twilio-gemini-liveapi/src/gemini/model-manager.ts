// Model Management System for Gemini
import { config, getConfigValue } from '../config/config.js';

interface ModelDefinition {
    name: string;
    description: string;
    status: string;
    supportsAudio: boolean;
    quality: string;
    region: string;
}

interface ModelValidationResult {
    isValid: boolean;
    model?: string;
    info?: ModelDefinition;
    error?: string;
    suggestion?: string;
    availableModels?: string[];
}

interface ModelConfig {
    defaultModel: string;
    currentModel: string;
    availableModels: Record<string, ModelDefinition>;
    modelSelectionEnabled: boolean;
    totalModels: number;
    configurationSource: {
        defaultModel: string;
        availableModels: string;
    };
}

interface ModelStats {
    totalAvailable: number;
    currentModel: string;
    defaultModel: string;
    audioSupportedModels: number;
    modelsByStatus: {
        'General availability': number;
        'Public preview': number;
        'Private preview': number;
    };
}

export class ModelManager {
    private defaultModel: string;
    private modelSelectionEnabled: boolean;
    private modelDefinitions: Record<string, ModelDefinition>;
    private availableModels: Record<string, ModelDefinition>;
    private currentModel: string;

    constructor() {
        this.defaultModel = getConfigValue('ai.gemini.defaultModel', 'gemini-2.5-flash-preview-native-audio-dialog') as string;
        this.modelSelectionEnabled = getConfigValue('ai.gemini.modelSelectionEnabled', false) as boolean;
        
        // Model definitions with metadata (Updated June 11, 2025)
        this.modelDefinitions = {
            'gemini-2.5-flash-preview-native-audio-dialog': {
                name: 'Gemini 2.5 Flash - Native Audio Dialog',
                description: 'Recommended default for voice apps. Outputs text and 24 kHz speech in 30 HD voices across 24 languages',
                status: 'Private preview',
                supportsAudio: true,
                quality: '★★★',
                region: 'global'
            },
            'gemini-2.0-flash-live-001': {
                name: 'Gemini 2.0 Flash Live (001)',
                description: 'GA/billing-enabled half-cascade audio. Native audio in, server-side TTS out',
                status: 'Public preview (billing on)',
                supportsAudio: true,
                quality: '★★',
                region: 'global, us-central1, EU4'
            },
            'gemini-2.0-flash': {
                name: 'Gemini 2.0 Flash (GA)',
                description: 'General availability model with text and audio support',
                status: 'General availability',
                supportsAudio: true,
                quality: '★★',
                region: 'global'
            },
            'gemini-2.0-flash-live-preview-04-09': {
                name: 'Gemini 2.0 Flash Live Preview (04-09)',
                description: 'Live API preview model with enhanced audio capabilities',
                status: 'Public preview',
                supportsAudio: true,
                quality: '★★',
                region: 'us-central1'
            }
        };

        // Load configuration from config system
        const availableModelIds = getConfigValue('ai.gemini.availableModels',
            ['gemini-2.5-flash-preview-native-audio-dialog', 'gemini-2.0-flash-live-001']) as string[];

        // Build available models object from environment configuration
        this.availableModels = {};
        availableModelIds.forEach(modelId => {
            if (this.modelDefinitions[modelId]) {
                this.availableModels[modelId] = this.modelDefinitions[modelId];
            } else {
                console.warn(`⚠️ Unknown model ID in GEMINI_AVAILABLE_MODELS: ${modelId}`);
            }
        });

        // Validate that default model is in available models
        if (!this.availableModels[this.defaultModel]) {
            console.error(`❌ Default model '${this.defaultModel}' not found in available models. Using fallback.`);
            const fallbackModel = Object.keys(this.availableModels)[0] || 'gemini-2.5-flash-preview-native-audio-dialog';
            console.log(`🔄 Using fallback model: ${fallbackModel}`);
            this.defaultModel = fallbackModel;
        }

        this.currentModel = this.defaultModel;
    }

    /**
     * Get all available models
     */
    getAvailableModels(): Record<string, ModelDefinition> {
        return this.availableModels;
    }

    /**
     * Get default model
     */
    getDefaultModel(): string {
        return this.defaultModel;
    }

    /**
     * Get current model
     */
    getCurrentModel(): string {
        return this.currentModel;
    }

    /**
     * Check if model selection is enabled
     */
    isModelSelectionEnabled(): boolean {
        return this.modelSelectionEnabled;
    }

    /**
     * Validate and get valid Gemini model
     */
    getValidGeminiModel(requestedModel?: string): string {
        if (!requestedModel) {
            console.log(`🤖 No model specified, using default: ${this.defaultModel}`);
            return this.defaultModel;
        }

        // Check if it's a valid Gemini model
        if (this.availableModels[requestedModel]) {
            console.log(`🤖 Using requested model: ${requestedModel}`);
            return requestedModel;
        }

        // Default fallback
        console.log(`⚠️ Unknown model '${requestedModel}', using default: ${this.defaultModel}`);
        return this.defaultModel;
    }

    /**
     * Get model information
     */
    getModelInfo(modelName: string): ModelDefinition | null {
        return this.availableModels[modelName] || null;
    }

    /**
     * Set current model
     */
    setCurrentModel(modelName: string): boolean {
        const validModel = this.getValidGeminiModel(modelName);
        if (validModel && this.availableModels[validModel]) {
            const previousModel = this.currentModel;
            this.currentModel = validModel;
            console.log(`🔄 MODEL CHANGED: '${previousModel}' → '${this.currentModel}'`);
            console.log(`🤖 NEW ACTIVE MODEL: ${this.currentModel} (${this.availableModels[this.currentModel]?.name || 'Unknown'})`);
            return true;
        }
        return false;
    }

    /**
     * Get model configuration for API response
     */
    getModelConfig(): ModelConfig {
        return {
            defaultModel: this.defaultModel,
            currentModel: this.currentModel,
            availableModels: this.availableModels,
            modelSelectionEnabled: this.modelSelectionEnabled,
            totalModels: Object.keys(this.availableModels).length,
            configurationSource: {
                defaultModel: process.env.GEMINI_DEFAULT_MODEL ? 'Environment' : 'Hardcoded',
                availableModels: process.env.GEMINI_AVAILABLE_MODELS ? 'Environment' : 'Hardcoded'
            }
        };
    }

    /**
     * Validate model name
     */
    validateModel(modelName: string): ModelValidationResult {
        if (!modelName) {
            return {
                isValid: false,
                error: 'Model name is required',
                suggestion: this.defaultModel
            };
        }

        if (this.availableModels[modelName]) {
            return {
                isValid: true,
                model: modelName,
                info: this.availableModels[modelName]
            };
        }

        return {
            isValid: false,
            error: `Unknown model '${modelName}'`,
            suggestion: this.defaultModel,
            availableModels: Object.keys(this.availableModels)
        };
    }

    /**
     * Get model statistics
     */
    getModelStats(): ModelStats {
        return {
            totalAvailable: Object.keys(this.availableModels).length,
            currentModel: this.currentModel,
            defaultModel: this.defaultModel,
            audioSupportedModels: Object.keys(this.availableModels).filter(
                modelId => this.availableModels[modelId].supportsAudio
            ).length,
            modelsByStatus: {
                'General availability': Object.keys(this.availableModels).filter(
                    modelId => this.availableModels[modelId].status === 'General availability'
                ).length,
                'Public preview': Object.keys(this.availableModels).filter(
                    modelId => this.availableModels[modelId].status?.includes('Public preview')
                ).length,
                'Private preview': Object.keys(this.availableModels).filter(
                    modelId => this.availableModels[modelId].status?.includes('Private preview')
                ).length
            }
        };
    }

    /**
     * Get recommended model for specific use case
     */
    getRecommendedModel(useCase: string = 'voice'): string {
        switch (useCase) {
            case 'voice':
                // Prefer models with better audio support
                if (this.availableModels['gemini-2.5-flash-preview-native-audio-dialog']) {
                    return 'gemini-2.5-flash-preview-native-audio-dialog';
                }
                if (this.availableModels['gemini-2.0-flash-live-001']) {
                    return 'gemini-2.0-flash-live-001';
                }
                break;
            case 'text':
                // Any available model works for text
                return this.defaultModel;
            case 'general':
            default:
                return this.defaultModel;
        }
        return this.defaultModel;
    }

    /**
     * Log current configuration
     */
    logConfiguration(): void {
        console.log(`🤖 Current Model: ${this.currentModel}`);
        console.log(`🤖 Default Model: ${this.defaultModel}`);
        console.log(`📋 Available Models: ${Object.keys(this.availableModels).join(', ')}`);
        console.log(`🔧 Model Selection: ${this.modelSelectionEnabled ? 'Enabled' : 'Disabled'}`);
        console.log(`⚙️ Configuration Source: ${process.env.GEMINI_DEFAULT_MODEL ? 'Environment' : 'Hardcoded'}`);
    }
}

// Create singleton instance
export const modelManager = new ModelManager();