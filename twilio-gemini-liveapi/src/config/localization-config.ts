// Localization Configuration System
// Replaces hardcoded language codes and locale settings

import { config, getConfigValue } from './config.js';

interface VoiceConfig {
    incoming: string;
    outbound: string;
}

interface ValidationMessages {
    vehicleYear: string;
    vehicleMake: string;
    vehicleModel: string;
    vehicleCount: string;
    claimsCount: string;
}

interface LocalizedTexts {
    recordingConfirmation: string;
    transferMessage: string;
    agentBusyMessage: string;
    defaultGreeting: string;
    validationMessages: ValidationMessages;
}

interface LanguageMapping {
    code: string;
    name: string;
    locale: string;
    voice: VoiceConfig;
    transcription: string;
}

interface LanguageMappings {
    [key: string]: LanguageMapping;
}

interface DefaultTexts {
    [key: string]: LocalizedTexts;
}

interface SupportedLanguage extends LanguageMapping {
    code: string;
}

/**
 * Localization Configuration Manager
 * Handles language settings, locale mappings, and text content
 */
export class LocalizationConfigManager {
    private defaultLanguage: string;
    private supportedLanguages: string[];
    private enableMultiLanguage: boolean;
    private fallbackLanguage: string;
    private languageMappings: LanguageMappings;
    private defaultTexts: DefaultTexts;

    constructor() {
        this.defaultLanguage = getConfigValue('localization.defaultLanguage', 'en') as string;
        this.supportedLanguages = getConfigValue('localization.supportedLanguages', ['en', 'es', 'cz']) as string[];
        this.enableMultiLanguage = getConfigValue('localization.enableMultiLanguage', true) as boolean;
        this.fallbackLanguage = getConfigValue('localization.fallbackLanguage', 'en') as string;
        
        // Language mappings
        this.languageMappings = {
            'en': {
                code: 'en',
                name: 'English',
                locale: 'en-US',
                voice: {
                    incoming: getConfigValue('voices.defaultVoiceMapping.en.incoming', 'empathetic') as string,
                    outbound: getConfigValue('voices.defaultVoiceMapping.en.outbound', 'relaxed') as string
                },
                transcription: 'en'
            },
            'es': {
                code: 'es',
                name: 'Spanish',
                locale: 'es-ES',
                voice: {
                    incoming: getConfigValue('voices.defaultVoiceMapping.es.incoming', 'empathetic') as string,
                    outbound: getConfigValue('voices.defaultVoiceMapping.es.outbound', 'energetic') as string
                },
                transcription: 'es'
            },
            'cz': {
                code: 'cz',
                name: 'Czech',
                locale: 'cs-CZ',
                voice: {
                    incoming: getConfigValue('voices.defaultVoiceMapping.cz.incoming', 'professional') as string,
                    outbound: getConfigValue('voices.defaultVoiceMapping.cz.outbound', 'authoritative') as string
                },
                transcription: 'cs'
            }
        };

        // Default text content (configurable)
        this.defaultTexts = {
            en: {
                recordingConfirmation: getConfigValue('security.recordingConfirmationMessage', 
                    'This call is recorded for quality assurance. Is it okay to continue?') as string,
                transferMessage: 'I will now transfer you to an agent. Please hold.',
                agentBusyMessage: 'All agents are busy. We will call you back.',
                defaultGreeting: 'Hello, how can I help you today?',
                validationMessages: {
                    vehicleYear: 'What year was that vehicle? Please provide a 4-digit year.',
                    vehicleMake: 'And the make?',
                    vehicleModel: 'And the model?',
                    vehicleCount: 'Please provide a valid number of vehicles (between 1 and 9).',
                    claimsCount: 'Please provide a valid number of claims.'
                }
            },
            es: {
                recordingConfirmation: 'Esta llamada se graba para garantía de calidad. ¿Está bien continuar?',
                transferMessage: 'Ahora lo transferiré a un agente. Por favor espere.',
                agentBusyMessage: 'Todos los agentes están ocupados. Le devolveremos la llamada.',
                defaultGreeting: 'Hola, ¿cómo puedo ayudarle hoy?',
                validationMessages: {
                    vehicleYear: '¿De qué año es ese vehículo? Proporcione un año de 4 dígitos.',
                    vehicleMake: '¿Y la marca?',
                    vehicleModel: '¿Y el modelo?',
                    vehicleCount: 'Proporcione un número válido de vehículos (entre 1 y 9).',
                    claimsCount: 'Proporcione un número válido de reclamaciones.'
                }
            },
            cz: {
                recordingConfirmation: 'Tento hovor je nahráván pro zajištění kvality. Je v pořádku pokračovat?',
                transferMessage: 'Nyní vás přepojím na agenta. Prosím čekejte.',
                agentBusyMessage: 'Všichni agenti jsou zaneprázdněni. Zavoláme vám zpět.',
                defaultGreeting: 'Dobrý den, jak vám mohu dnes pomoci?',
                validationMessages: {
                    vehicleYear: 'Jakého roku je to vozidlo? Uveďte prosím 4místný rok.',
                    vehicleMake: 'A značka?',
                    vehicleModel: 'A model?',
                    vehicleCount: 'Uveďte prosím platný počet vozidel (mezi 1 a 9).',
                    claimsCount: 'Uveďte prosím platný počet pojistných událostí.'
                }
            }
        };
    }

    /**
     * Get language configuration
     */
    public getLanguageConfig(languageCode: string): LanguageMapping {
        const normalizedCode = this.normalizeLanguageCode(languageCode);
        return this.languageMappings[normalizedCode] || this.languageMappings[this.fallbackLanguage];
    }

    /**
     * Get voice for language and call type
     */
    public getVoiceForLanguage(languageCode: string, callType: 'incoming' | 'outbound' = 'outbound'): string {
        const langConfig = this.getLanguageConfig(languageCode);
        return langConfig.voice[callType] || langConfig.voice.outbound;
    }

    /**
     * Get localized text
     */
    public getLocalizedText(languageCode: string, textKey: string, fallback: string = ''): string {
        const normalizedCode = this.normalizeLanguageCode(languageCode);
        const texts = this.defaultTexts[normalizedCode] || this.defaultTexts[this.fallbackLanguage];
        
        // Support nested keys like 'validationMessages.vehicleYear'
        const keys = textKey.split('.');
        let value: any = texts;
        
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return fallback;
            }
        }
        
        return (typeof value === 'string' ? value : fallback);
    }

    /**
     * Normalize language code
     */
    public normalizeLanguageCode(languageCode: string | undefined): string {
        if (!languageCode) return this.defaultLanguage;
        
        // Handle various formats: 'en-US' -> 'en', 'EN' -> 'en'
        const normalized = languageCode.toLowerCase().split('-')[0];
        
        // Check if supported
        if (this.supportedLanguages.includes(normalized)) {
            return normalized;
        }
        
        return this.fallbackLanguage;
    }

    /**
     * Get all supported languages
     */
    public getSupportedLanguages(): SupportedLanguage[] {
        return this.supportedLanguages.map(code => ({
            code,
            ...this.languageMappings[code]
        }));
    }

    /**
     * Check if language is supported
     */
    public isLanguageSupported(languageCode: string): boolean {
        const normalized = this.normalizeLanguageCode(languageCode);
        return this.supportedLanguages.includes(normalized);
    }

    /**
     * Get transcription language code
     */
    public getTranscriptionLanguage(languageCode: string): string {
        const langConfig = this.getLanguageConfig(languageCode);
        return langConfig.transcription || this.fallbackLanguage;
    }

    /**
     * Get locale string for formatting
     */
    public getLocale(languageCode: string): string {
        const langConfig = this.getLanguageConfig(languageCode);
        return langConfig.locale || 'en-US';
    }

    /**
     * Update language configuration at runtime
     */
    public updateLanguageConfig(languageCode: string, config: Partial<LanguageMapping>): void {
        const normalizedCode = this.normalizeLanguageCode(languageCode);
        if (this.languageMappings[normalizedCode]) {
            this.languageMappings[normalizedCode] = {
                ...this.languageMappings[normalizedCode],
                ...config
            };
        }
    }

    /**
     * Update localized text at runtime
     */
    public updateLocalizedText(languageCode: string, textKey: string, value: string): void {
        const normalizedCode = this.normalizeLanguageCode(languageCode);
        if (!this.defaultTexts[normalizedCode]) {
            this.defaultTexts[normalizedCode] = {} as LocalizedTexts;
        }
        
        // Support nested keys
        const keys = textKey.split('.');
        let target: any = this.defaultTexts[normalizedCode];
        
        for (let i = 0; i < keys.length - 1; i++) {
            const key = keys[i];
            if (!target[key] || typeof target[key] !== 'object') {
                target[key] = {};
            }
            target = target[key];
        }
        
        target[keys[keys.length - 1]] = value;
    }

    /**
     * Get configuration summary
     */
    public getConfigSummary(): {
        defaultLanguage: string;
        supportedLanguages: string[];
        enableMultiLanguage: boolean;
        fallbackLanguage: string;
        availableTexts: string[];
    } {
        return {
            defaultLanguage: this.defaultLanguage,
            supportedLanguages: this.supportedLanguages,
            enableMultiLanguage: this.enableMultiLanguage,
            fallbackLanguage: this.fallbackLanguage,
            availableTexts: Object.keys(this.defaultTexts)
        };
    }
}

// Export singleton instance
export const localizationConfigManager = new LocalizationConfigManager();

// Export utility functions
export function getLanguageConfig(languageCode: string): LanguageMapping {
    return localizationConfigManager.getLanguageConfig(languageCode);
}

export function getVoiceForLanguage(languageCode: string, callType: 'incoming' | 'outbound' = 'outbound'): string {
    return localizationConfigManager.getVoiceForLanguage(languageCode, callType);
}

export function getLocalizedText(languageCode: string, textKey: string, fallback: string = ''): string {
    return localizationConfigManager.getLocalizedText(languageCode, textKey, fallback);
}

export function normalizeLanguageCode(languageCode: string | undefined): string {
    return localizationConfigManager.normalizeLanguageCode(languageCode);
}

export function isLanguageSupported(languageCode: string): boolean {
    return localizationConfigManager.isLanguageSupported(languageCode);
}