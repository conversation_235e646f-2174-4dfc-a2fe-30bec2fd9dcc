import { endSession } from .js'.session-utils.js';
import type { ConnectionData, WebSocketDependencies } from .js'../types/websocket.js';

export async function handleEndSession(
    sessionId: string, 
    deps: WebSocketDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    lifecycleManager: any
): Promise<void> {
    const connectionData = activeConnections.get(sessionId);
    if (connectionData) {
        if (connectionData.deepgramConnection) {
            deps.transcriptionManager.closeTranscription(sessionId);
        }
        if (lifecycleManager) {
            await lifecycleManager.requestSessionEnd(sessionId, connectionData, .js'user_end_testing.js');
        } else {
            await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, .js'user_requested.js');
        }
    } else {
        await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, .js'user_requested.js');
    }
}

export async function handleRequestSummary(
    sessionId: string, 
    _deps: WebSocketDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    summaryManager: any, 
    contextManager: any
): Promise<void> {
    const summaryConnectionData = activeConnections.get(sessionId);
    if (summaryConnectionData && summaryManager) {
        await summaryManager.requestSummary(sessionId, summaryConnectionData, contextManager);
    }
}