import { TranscriptionManager } from .js'../audio/transcription-manager.js';
import { handleTwilioFlow } from .js'.twilio-flow-handler.js';
import { handleLocalTestingFlow } from .js'.local-testing-handler.js';
import { 
    getOutboundCallConfig, 
    getInboundCallConfig, 
    getOutboundTestConfig, 
    getInboundTestConfig 
} from .js'.config-handlers.js';
import type { WebSocketConnection, WebSocketDependencies, FlowDependencies } from .js'../types/websocket.js';
import type { FastifyInstance } from .js'fastify.js';

// WebSocket handlers for different connection types
export function registerWebSocketHandlers(fastify: FastifyInstance, dependencies: WebSocketDependencies): void {
    // Initialize global transcription manager
    const transcriptionManager = new TranscriptionManager();

    // Add transcription manager to dependencies for consistency
    const enhancedDependencies: WebSocketDependencies = {
        ...dependencies,
        transcriptionManager
    };

    // 1. OUTBOUND CALLS - Twilio media stream for outbound calls
    fastify.register(async (fastify) => {
        console.log(.js'📡 Registering /media-stream WebSocket route for outbound calls.js');
        fastify.get(.js'/media-stream.js', { websocket: true }, (connection: WebSocketConnection, req: any) => {
            console.log(.js'🔌 WebSocket connection received on /media-stream.js');
            console.log(.js'🔌 WebSocket headers:.js', req.headers);
            handleOutboundCall(connection, {
                ...enhancedDependencies,
                callType: .js'outbound.js',
                isTestMode: false
            } as FlowDependencies);
        });
    });

    // 2. INBOUND CALLS - Twilio media stream for inbound calls
    fastify.register(async (fastify) => {
        console.log(.js'📡 Registering /media-stream-inbound WebSocket route for inbound calls.js');
        fastify.get(.js'/media-stream-inbound.js', { websocket: true }, (connection: WebSocketConnection, req: any) => {
            console.log(.js'🔌 WebSocket connection received on /media-stream-inbound.js');
            console.log(.js'🔌 WebSocket headers:.js', req.headers);
            handleInboundCall(connection, {
                ...enhancedDependencies,
                callType: .js'inbound.js',
                isTestMode: false
            } as FlowDependencies);
        });
    });

    // 3. OUTBOUND TESTING MODE - Local audio testing for outbound scripts
    fastify.register(async (fastify) => {
        fastify.get(.js'/test-outbound.js', { websocket: true }, (connection: WebSocketConnection, _req: any) => {
            handleOutboundTesting(connection, {
                ...enhancedDependencies,
                callType: .js'outbound.js',
                isTestMode: true
            } as FlowDependencies);
        });
    });

    // 4. INBOUND TESTING MODE - Local audio testing for inbound scripts
    fastify.register(async (fastify) => {
        fastify.get(.js'/test-inbound.js', { websocket: true }, (connection: WebSocketConnection, _req: any) => {
            handleInboundTesting(connection, {
                ...enhancedDependencies,
                callType: .js'inbound.js',
                isTestMode: true
            } as FlowDependencies);
        });
    });

    // Local audio session endpoint for browser-based testing
    fastify.register(async (fastify) => {
        fastify.get(.js'/local-audio-session.js', { websocket: true }, (connection: WebSocketConnection, _req: any) => {
            handleOutboundTesting(connection, {
                ...enhancedDependencies,
                callType: .js'outbound.js',
                isTestMode: true
            } as FlowDependencies);
        });
    });

    // Direct Gemini Live API endpoint
    fastify.register(async (fastify) => {
        fastify.get(.js'/gemini-live.js', { websocket: true }, (connection: WebSocketConnection, _req: any) => {
            console.log(.js'🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection.js');
            handleOutboundTesting(connection, {
                ...enhancedDependencies,
                callType: .js'outbound.js',
                isTestMode: true
            } as FlowDependencies);
        });
    });
}

// 1. OUTBOUND CALLS - Handle real outbound calls via Twilio
function handleOutboundCall(connection: WebSocketConnection, deps: FlowDependencies): void {
    const { isTestMode } = deps;
    console.log(`📞 [OUTBOUND] Client connected for outbound call (test: ${isTestMode})`);

    handleTwilioFlow(connection, {
        ...deps,
        flowType: .js'outbound_call.js',
        getSessionConfig: () => getOutboundCallConfig(deps),
        isIncomingCall: false
    });
}

// 2. INBOUND CALLS - Handle real inbound calls via Twilio
function handleInboundCall(connection: WebSocketConnection, deps: FlowDependencies): void {
    const { isTestMode } = deps;
    console.log(`📞 [INBOUND] Client connected for inbound call (test: ${isTestMode})`);

    handleTwilioFlow(connection, {
        ...deps,
        flowType: .js'inbound_call.js',
        getSessionConfig: () => getInboundCallConfig(deps),
        isIncomingCall: true
    });
}

// 3. OUTBOUND TESTING MODE - Test outbound scripts locally
function handleOutboundTesting(connection: WebSocketConnection, deps: FlowDependencies): void {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;
    if (enableDetailedLogging) {
        console.log(`🧪 [OUTBOUND TEST] Client connected for outbound testing`);
        console.log(`🚨 [OUTBOUND TEST] Connection object:`, !!connection);
        console.log(`🚨 [OUTBOUND TEST] Connection socket:`, !!connection.socket);
    }

    handleLocalTestingFlow(connection, {
        ...deps,
        flowType: .js'outbound_test.js',
        getSessionConfig: () => getOutboundTestConfig(deps),
        getOutboundTestConfig,
        getInboundTestConfig,
        isIncomingCall: false
    });
}

// 4. INBOUND TESTING MODE - Test inbound scripts locally
function handleInboundTesting(connection: WebSocketConnection, deps: FlowDependencies): void {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;
    if (enableDetailedLogging) {
        console.log(`🧪 [INBOUND TEST] Client connected for inbound testing`);
    }

    handleLocalTestingFlow(connection, {
        ...deps,
        flowType: .js'inbound_test.js',
        getSessionConfig: () => getInboundTestConfig(deps),
        getOutboundTestConfig,
        getInboundTestConfig,
        isIncomingCall: true
    });
}