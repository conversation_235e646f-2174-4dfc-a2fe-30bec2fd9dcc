import { TranscriptionManager } from '../audio/transcription-manager.js';
import { handleTwilioFlow } from './twilio-flow-handler.js';
import { handleLocalTestingFlow } from './local-testing-handler.js';
import { 
    getOutboundCallConfig, 
    getInboundCallConfig, 
    getOutboundTestConfig, 
    getInboundTestConfig 
} from './config-handlers.js';
import type { WebSocketConnection, WebSocketDependencies, FlowDependencies } from '../types/websocket.js';
import type { FastifyInstance } from 'fastify';

// WebSocket handlers for different connection types
export function registerWebSocketHandlers(fastify: FastifyInstance, dependencies: WebSocketDependencies): void {
    // Initialize global transcription manager
    const transcriptionManager = new TranscriptionManager();

    // Add transcription manager to dependencies for consistency
    const enhancedDependencies: WebSocketDependencies = {
        ...dependencies,
        transcriptionManager
    };

    // 1. OUTBOUND CALLS - Twilio media stream for outbound calls
    fastify.register(async (fastify) => {
        console.log('📡 Registering /media-stream WebSocket route for outbound calls');
        fastify.get('/media-stream', { websocket: true }, (connection: WebSocketConnection, req: any) => {
            console.log('🔌 WebSocket connection received on /media-stream');
            console.log('🔌 WebSocket headers:', req.headers);
            handleOutboundCall(connection, {
                ...enhancedDependencies,
                callType: 'outbound',
                isTestMode: false
            } as FlowDependencies);
        });
    });

    // 2. INBOUND CALLS - Twilio media stream for inbound calls
    fastify.register(async (fastify) => {
        console.log('📡 Registering /media-stream-inbound WebSocket route for inbound calls');
        fastify.get('/media-stream-inbound', { websocket: true }, (connection: WebSocketConnection, req: any) => {
            console.log('🔌 WebSocket connection received on /media-stream-inbound');
            console.log('🔌 WebSocket headers:', req.headers);
            handleInboundCall(connection, {
                ...enhancedDependencies,
                callType: 'inbound',
                isTestMode: false
            } as FlowDependencies);
        });
    });

    // 3. OUTBOUND TESTING MODE - Local audio testing for outbound scripts
    fastify.register(async (fastify) => {
        fastify.get('/test-outbound', { websocket: true }, (connection: WebSocketConnection, _req: any) => {
            handleOutboundTesting(connection, {
                ...enhancedDependencies,
                callType: 'outbound',
                isTestMode: true
            } as FlowDependencies);
        });
    });

    // 4. INBOUND TESTING MODE - Local audio testing for inbound scripts
    fastify.register(async (fastify) => {
        fastify.get('/test-inbound', { websocket: true }, (connection: WebSocketConnection, _req: any) => {
            handleInboundTesting(connection, {
                ...enhancedDependencies,
                callType: 'inbound',
                isTestMode: true
            } as FlowDependencies);
        });
    });

    // Local audio session endpoint for browser-based testing
    fastify.register(async (fastify) => {
        fastify.get('/local-audio-session', { websocket: true }, (connection: WebSocketConnection, _req: any) => {
            handleOutboundTesting(connection, {
                ...enhancedDependencies,
                callType: 'outbound',
                isTestMode: true
            } as FlowDependencies);
        });
    });

    // Direct Gemini Live API endpoint
    fastify.register(async (fastify) => {
        fastify.get('/gemini-live', { websocket: true }, (connection: WebSocketConnection, _req: any) => {
            console.log('🔗 [GEMINI-LIVE] Direct Gemini Live WebSocket connection');
            handleOutboundTesting(connection, {
                ...enhancedDependencies,
                callType: 'outbound',
                isTestMode: true
            } as FlowDependencies);
        });
    });
}

// 1. OUTBOUND CALLS - Handle real outbound calls via Twilio
function handleOutboundCall(connection: WebSocketConnection, deps: FlowDependencies): void {
    const { isTestMode } = deps;
    console.log(`📞 [OUTBOUND] Client connected for outbound call (test: ${isTestMode})`);

    handleTwilioFlow(connection, {
        ...deps,
        flowType: 'outbound_call',
        getSessionConfig: () => getOutboundCallConfig(deps),
        isIncomingCall: false
    });
}

// 2. INBOUND CALLS - Handle real inbound calls via Twilio
function handleInboundCall(connection: WebSocketConnection, deps: FlowDependencies): void {
    const { isTestMode } = deps;
    console.log(`📞 [INBOUND] Client connected for inbound call (test: ${isTestMode})`);

    handleTwilioFlow(connection, {
        ...deps,
        flowType: 'inbound_call',
        getSessionConfig: () => getInboundCallConfig(deps),
        isIncomingCall: true
    });
}

// 3. OUTBOUND TESTING MODE - Test outbound scripts locally
function handleOutboundTesting(connection: WebSocketConnection, deps: FlowDependencies): void {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;
    if (enableDetailedLogging) {
        console.log(`🧪 [OUTBOUND TEST] Client connected for outbound testing`);
        console.log(`🚨 [OUTBOUND TEST] Connection object:`, !!connection);
        console.log(`🚨 [OUTBOUND TEST] Connection socket:`, !!connection.socket);
    }

    handleLocalTestingFlow(connection, {
        ...deps,
        flowType: 'outbound_test',
        getSessionConfig: () => getOutboundTestConfig(deps),
        getOutboundTestConfig,
        getInboundTestConfig,
        isIncomingCall: false
    });
}

// 4. INBOUND TESTING MODE - Test inbound scripts locally
function handleInboundTesting(connection: WebSocketConnection, deps: FlowDependencies): void {
    const enableDetailedLogging = deps.config?.environment?.enableDetailedLogging;
    if (enableDetailedLogging) {
        console.log(`🧪 [INBOUND TEST] Client connected for inbound testing`);
    }

    handleLocalTestingFlow(connection, {
        ...deps,
        flowType: 'inbound_test',
        getSessionConfig: () => getInboundTestConfig(deps),
        getOutboundTestConfig,
        getInboundTestConfig,
        isIncomingCall: true
    });
}