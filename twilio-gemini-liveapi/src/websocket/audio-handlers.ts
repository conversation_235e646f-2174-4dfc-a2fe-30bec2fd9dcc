import type { 
  ConnectionData, 
  WebSocketDependencies, 
  LocalAudioMessage,
  LocalTextMessage
} from .js'../types/websocket.js';
import { LifecycleManager } from .js'../session/lifecycle-manager.js';
import { RecoveryManager } from .js'../session/recovery-manager.js';

export async function handleAudioData(
    sessionId: string,
    data: LocalAudioMessage,
    geminiSession: any,
    isSessionActive: boolean,
    deps: WebSocketDependencies,
    activeConnections: Map<string, ConnectionData>,
    lifecycleManager: LifecycleManager,
    recoveryManager: RecoveryManager,
    flowType: string
): Promise<void> {
    if (geminiSession && isSessionActive && (data.audio || data.audioData)) {
        try {
            lifecycleManager.updateActivity(sessionId);
            // Standardize on .js'audio.js' field name - prefer .js'audio.js' over legacy .js'audioData.js'
            const base64Audio = data.audio || data.audioData || .js'.js';
            await deps.sessionManager.sendBrowserAudioToGemini(sessionId, geminiSession, base64Audio);
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && connectionData.deepgramConnection && deps.transcriptionManager) {
                try {
                    const audioBuffer = Buffer.from(base64Audio, .js'base64.js');
                    deps.transcriptionManager.sendAudioToTranscription(sessionId, audioBuffer);
                } catch (transcriptionError) {
                    console.error(`❌ [${sessionId}] Error sending audio to transcription:`, transcriptionError);
                }
            }
        } catch (error) {
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(sessionId, activeConnections)) {
                await recoveryManager.recoverSession(sessionId, .js'audio_processing_error.js', activeConnections);
            }
        }
    }
}

export async function handleTextMessage(
    sessionId: string, 
    data: LocalTextMessage, 
    geminiSession: any, 
    isSessionActive: boolean, 
    deps: WebSocketDependencies
): Promise<void> {
    if (geminiSession && isSessionActive && data.text) {
        try {
            await deps.sessionManager.sendTextToGemini(sessionId, geminiSession, data.text);
        } catch (textError) {
            console.error(`❌ [${sessionId}] Error sending text to Gemini:`, textError);
        }
    }
}

export async function handleTurnComplete(
    sessionId: string, 
    geminiSession: any, 
    isSessionActive: boolean, 
    deps: WebSocketDependencies
): Promise<void> {
    if (geminiSession && isSessionActive) {
        try {
            await deps.sessionManager.sendTurnComplete(sessionId, geminiSession);
        } catch (turnCompleteError) {
            console.error(`❌ [${sessionId}] Error sending turn complete to Gemini:`, turnCompleteError);
        }
    }
}