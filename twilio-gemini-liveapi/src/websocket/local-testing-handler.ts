import { endSession } from './session-utils.js';
import { websocketLogger } from '../utils/logger.js';
import { globalHeartbeatManager } from './heartbeat-manager.js';
import pkg from '@google/genai';
const { Modality } = pkg;
import type { 
    WebSocketConnection, 
    FlowDependencies, 
    ConnectionData,
    SessionData,
    LocalStartMessage,
    LocalAudioMessage,
    LocalTextMessage
} from '../types/websocket.js';
import type { WebSocket } from 'ws';

// Common local testing flow handler (for both inbound and outbound testing)
export function handleLocalTestingFlow(connection: WebSocketConnection, deps: FlowDependencies): void {
    const { config } = deps;
    const enableDetailedLogging = config?.environment?.enableDetailedLogging;
    
    if (enableDetailedLogging) {
        console.log(`🔥🔥🔥 HANDLELOCALTESTINGFLOW CALLED! FlowType: ${deps.flowType} 🔥🔥🔥`);
    }
    
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        summaryManager,
        lifecycleManager,
        recoveryManager,
        transcriptionManager,
        flowType,
        getSessionConfig,
        isIncomingCall,
        SUMMARY_GENERATION_PROMPT
    } = deps;

    const sessionId = `${flowType}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    if (enableDetailedLogging) {
        console.log(`🔥 [${sessionId}] About to log testing session started`);
    }
    websocketLogger.info(`${flowType.toUpperCase()} testing session started`, { sessionId });
    if (enableDetailedLogging) {
        websocketLogger.debug(`Setting up message handler for ${flowType} testing`, { sessionId });
        websocketLogger.debug('Step 1: About to check WebSocket state', { sessionId });
    }

    try {
        if (enableDetailedLogging) {
            websocketLogger.debug('Connection object inspected', { sessionId, hasConnection: !!connection });
            websocketLogger.debug('Connection keys', { sessionId, keys: Object.keys(connection) });
            websocketLogger.debug('Socket object present', { sessionId, hasSocket: !!connection.socket });
        }

        // Try different possible WebSocket references
        const ws = connection.socket || connection;
        if (enableDetailedLogging) {
            websocketLogger.debug('Using WebSocket', { sessionId, hasWs: !!ws });
            websocketLogger.debug('WebSocket readyState', { sessionId, readyState: (ws as WebSocket).readyState });
            websocketLogger.debug('WebSocket protocol', { sessionId, protocol: (ws as WebSocket).protocol });
            websocketLogger.debug('Step 3: About to declare variables', { sessionId });
        }
    } catch (wsError) {
        console.error(`❌ [${sessionId}] Error checking WebSocket state:`, wsError);
        if (enableDetailedLogging) {
            console.error(`❌ [${sessionId}] Error stack:`, (wsError as Error).stack);
        }
        // Don't return early - continue with handler setup
        if (enableDetailedLogging) {
            websocketLogger.debug('Continuing with handler setup despite WebSocket error', { sessionId });
        }
    }

    let geminiSession: any = null;
    let isSessionActive = false;
    if (enableDetailedLogging) {
        websocketLogger.debug('Step 4: Variables declared, about to attach message handler', { sessionId });
    }

    // Use the correct WebSocket reference
    const ws = (connection.socket || connection) as WebSocket;
    if (enableDetailedLogging) {
        websocketLogger.debug('Attaching message handler', { sessionId, hasWs: !!ws });
    }

    // Store event listeners for cleanup
    const eventListeners = new Map<string, Function>();

    const messageHandler = async (message: Buffer | string) => {
        if (enableDetailedLogging) {
            console.log(`🚨🚨🚨 [${sessionId}] MESSAGE HANDLER CALLED! 🚨🚨🚨`);
            websocketLogger.debug('MESSAGE HANDLER CALLED!', { sessionId });
            websocketLogger.debug('Raw message received', { sessionId, preview: message.toString().substring(0, 200) });
        }
        try {
            const data = JSON.parse(message.toString());
            if (enableDetailedLogging) {
                console.log(`🔍 [${sessionId}] OUTBOUND TEST: Received message type: ${data.type}`);
                websocketLogger.debug(`Parsed message type: ${data.type}`, { sessionId });
                websocketLogger.debug('Message data keys', { sessionId, keys: Object.keys(data) });
                websocketLogger.debug('===== MESSAGE SWITCH DEBUG =====', { sessionId });
                websocketLogger.debug(`data.type = "${data.type}"`, { sessionId });
                websocketLogger.debug('About to enter switch statement', { sessionId });
                websocketLogger.debug('=====================================', { sessionId });
            }

            switch (data.type) {
            case 'start-session':
                if (enableDetailedLogging) {
                    console.log(`🔍 [${sessionId}] ENTERING START-SESSION HANDLER for ${flowType}`);
                }
                const sessionData = await handleLocalStartSession(sessionId, data, deps, connection, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager);
                geminiSession = sessionData.geminiSession;
                isSessionActive = sessionData.isSessionActive;
                if (enableDetailedLogging) {
                    console.log(`🔍 [${sessionId}] START-SESSION HANDLER COMPLETED, geminiSession: ${!!geminiSession}, isSessionActive: ${isSessionActive}`);
                }
                break;

            case 'audio-data':
            case 'audio': // Support both audio-data and audio message types
                await handleAudioData(sessionId, data, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager, flowType);
                break;

            case 'text-message':
                await handleTextMessage(sessionId, data, geminiSession, isSessionActive, deps);
                break;

            case 'turn-complete':
                await handleTurnComplete(sessionId, geminiSession, isSessionActive, deps);
                break;

            case 'end-session':
                await handleEndSession(sessionId, deps, activeConnections, lifecycleManager);
                break;

            case 'request-summary':
                await handleRequestSummary(sessionId, deps, activeConnections, summaryManager, contextManager);
                break;

            case 'heartbeat':
                // Handle heartbeat messages silently - just acknowledge
                websocketLogger.debug(`Heartbeat received for ${flowType} testing`, { sessionId });
                break;

            case 'audio-response':
                // Handle audio-response events (frontend might be echoing back audio)
                websocketLogger.debug(`Audio response received for ${flowType} testing`, { sessionId });
                // Don't process these - they're just echoes from the frontend
                break;

            default:
                websocketLogger.warn(`Unknown ${flowType} testing event: ${data.type}`, { sessionId });
            }

        } catch (error) {
            console.error(`❌ [${sessionId}] Error processing ${flowType} testing message:`, error);
        }
    };

    // Store handlers for cleanup
    eventListeners.set('message', messageHandler);
    console.log(`🚨 [${sessionId}] ATTACHING MESSAGE HANDLER TO WebSocket`);
    console.log(`🚨 [${sessionId}] WebSocket readyState: ${ws.readyState}`);
    ws.on('message', messageHandler);

    const closeHandler = async (code: number, reason: Buffer) => {
        websocketLogger.info(`${flowType.toUpperCase()} testing connection closed`, {
            sessionId,
            code,
            reason: reason ? reason.toString() : 'No reason'
        });

        // Stop heartbeat monitoring
        globalHeartbeatManager.stopHeartbeat(sessionId);
        // This is a user-initiated session end via connection close
        const connectionData = activeConnections.get(sessionId);
        if (connectionData) {
            // Clean up Deepgram transcription
            if (connectionData.deepgramConnection) {
                deps.transcriptionManager.closeTranscription(sessionId);
            }

            if (deps.lifecycleManager) {
                await deps.lifecycleManager.requestSessionEnd(sessionId, connectionData, 'user_close_testing');
            } else {
                endSession(sessionId, deps, 'connection_closed');
            }
        } else {
            endSession(sessionId, deps, 'connection_closed');
        }

        // Clean up event listeners
        for (const [event, handler] of eventListeners) {
            ws.removeListener(event, handler as any);
        }
        eventListeners.clear();
    };

    const errorHandler = async (error: Error) => {
        websocketLogger.error(`${flowType.toUpperCase()} testing error`, { sessionId, error });
        websocketLogger.error('Error stack', { sessionId, stack: error.stack });
        // WebSocket error in testing - try to recover session instead of ending it
        const connectionData = activeConnections.get(sessionId);
        if (connectionData && recoveryManager && contextManager.canRecover(sessionId)) {
            websocketLogger.info('Testing WebSocket error detected, attempting session recovery', { sessionId });
            contextManager.markSessionInterrupted(sessionId, 'testing_websocket_error');
            // Don't end session immediately - let recovery manager handle it
            setTimeout(async () => {
                await recoveryManager.recoverSession(sessionId, 'testing_websocket_error', activeConnections);
            }, 1000);
        } else {
            // Clean up Deepgram transcription before ending session
            if (connectionData && connectionData.deepgramConnection) {
                deps.transcriptionManager.closeTranscription(sessionId);
            }
            // Only end session if recovery is not possible
            endSession(sessionId, { ...deps, transcriptionManager }, 'connection_error');
        }
    };

    // Register all event listeners and store for cleanup
    eventListeners.set('close', closeHandler);
    eventListeners.set('error', errorHandler);
    ws.on('close', closeHandler);
    ws.on('error', errorHandler);
}

// Helper functions for message handling
async function handleLocalStartSession(
    sessionId: string, 
    data: LocalStartMessage, 
    deps: FlowDependencies, 
    connection: WebSocketConnection, 
    ws: WebSocket, 
    flowType: string, 
    isIncomingCall: boolean, 
    getSessionConfig: () => any, 
    activeConnections: Map<string, ConnectionData>, 
    healthMonitor: any, 
    lifecycleManager: any, 
    sessionManager: any
): Promise<SessionData> {
    console.log(`🔍 [${sessionId}] ===== ENTERED START-SESSION CASE =====`);
    console.log(`🚀 [${sessionId}] Starting ${flowType} testing session`);

    try {
        // Get flow-specific configuration
        console.log(`🔍 DEBUG: [${sessionId}] About to call getSessionConfig for ${flowType}`);
        let sessionConfig = getSessionConfig();
        console.log(`🔍 DEBUG: [${sessionId}] getSessionConfig returned:`, !!sessionConfig);

        if (sessionConfig) {
            console.log(`🔍 DEBUG: [${sessionId}] Initial session config:`, {
                hasAiInstructions: !!sessionConfig.aiInstructions,
                aiInstructionsLength: sessionConfig.aiInstructions?.length || 0,
                voice: sessionConfig.voice,
                model: sessionConfig.model,
                scriptType: sessionConfig.scriptType,
                scriptId: sessionConfig.scriptId
            });
        } else {
            console.error(`❌ [${sessionId}] getSessionConfig returned null/undefined for ${flowType}`);
        }

        // Allow override from client for testing - use campaign script as-is
        if (data.aiInstructions) {
            sessionConfig.aiInstructions = data.aiInstructions;
        }
        if (data.voice) {
            sessionConfig.voice = deps.voiceManager.getValidGeminiVoice(data.voice);
        }
        if (data.model) {
            sessionConfig.model = deps.modelManager.getValidGeminiModel(data.model);
        }
        if (data.scriptId) {
            // Load specific script for testing
            try {
                const testConfig = deps.scriptManager.getScriptConfig(data.scriptId, isIncomingCall);
                if (testConfig) {
                    sessionConfig = {
                        ...testConfig,
                        aiInstructions: `[TESTING MODE] ${testConfig.aiInstructions}`,
                        isTestMode: true
                    };
                }
            } catch (error) {
                console.warn(`⚠️ [${sessionId}] Error loading test script ${data.scriptId}:`, error);
            }
        }

        // Store enhanced connection data
        const connectionData: ConnectionData = {
            ws: connection.socket || connection, // Standardized WebSocket property name
            localWs: connection.socket || connection,
            sessionId,
            isSessionActive: false,
            summaryRequested: false,
            summaryReceived: false,
            summaryText: '',
            conversationLog: [],
            fullTranscript: [],
            speechTranscript: [],
            isIncomingCall,
            sessionType: 'local_test',
            flowType,
            sessionStartTime: Date.now(),
            lastActivity: Date.now(),
            targetName: sessionConfig.targetName || 'Test Contact',
            targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
            originalAIInstructions: sessionConfig.aiInstructions,
            scriptId: sessionConfig.scriptId,
            isTestMode: true,
            // CRITICAL: Add missing turn management properties
            lastAIResponse: Date.now(), // Track AI responsiveness
            responseTimeouts: 0, // Count consecutive timeouts
            connectionQuality: 'good', // Track connection quality
            lastContextSave: Date.now(), // For periodic context saving
            contextSaveInterval: null // For periodic context saving
        };
        activeConnections.set(sessionId, connectionData);

        // Track connection health
        healthMonitor.trackConnection(sessionId, 'connected', {
            flowType,
            isTestMode: true,
            scriptId: sessionConfig.scriptId
        });

        // Create Gemini session
        console.log(`🔍 DEBUG: [${sessionId}] About to create Gemini session...`);

        const sessionStartTime = Date.now();
        let geminiSession: any = null;
        let isSessionActive = false;

        try {
            // Use the correct model and voice from environment/managers
            const correctModel = sessionConfig.model || deps.GEMINI_DEFAULT_MODEL;
            const correctVoice = sessionConfig.voice || deps.GEMINI_DEFAULT_VOICE;
            console.log(`🔍 [${sessionId}] FINAL: Using model: "${correctModel}", voice: "${correctVoice}"`);

            console.log(`🔍 [${sessionId}] About to call geminiClient.live.connect...`);
            console.log(`🔍 [${sessionId}] geminiClient available: ${!!deps.sessionManager.geminiClient}`);
            console.log(`🔍 [${sessionId}] geminiClient.live available: ${!!deps.sessionManager.geminiClient?.live}`);
            
            const geminiStartTime = Date.now();
            console.log(`⏱️ [${sessionId}] Starting Gemini connection at ${new Date(geminiStartTime).toISOString()}`);
            
            // Live API setup is now handled by sessionManager.createGeminiSession()
            
            console.log(`🔍 [${sessionId}] About to create Gemini session with model: ${correctModel}`);
            console.log(`🔍 [${sessionId}] Using sessionManager.createGeminiSession() for standardized session creation`);

            // Use sessionManager for standardized session creation (same as Twilio flows)
            geminiSession = await deps.sessionManager.createGeminiSession(sessionId, {
                model: correctModel,
                voice: correctVoice,
                aiInstructions: sessionConfig.aiInstructions
            }, connectionData);

            if (geminiSession) {
                isSessionActive = true;
                console.log(`✅ [${sessionId}] Gemini session created via sessionManager`);
            } else {
                throw new Error('Failed to create Gemini session via sessionManager');
            }

            // Session is now created and configured by sessionManager
            // No need for additional setup - sessionManager handles Live API configuration

            // Start WebSocket heartbeat monitoring for local testing
            globalHeartbeatManager.startHeartbeat(
                sessionId,
                ws,
                30000, // 30 second ping interval
                10000, // 10 second pong timeout
                (sessionId: string, ws: WebSocket) => {
                    console.log(`💔 [${sessionId}] WebSocket heartbeat timeout in local testing - ending session`);
                    endSession(sessionId, { activeConnections, lifecycleManager } as any, 'heartbeat_timeout');
                }
            );

        } catch (error) {
            console.error(`❌ [${sessionId}] Error creating DIRECT Gemini session:`, error);
            geminiSession = null;
        }

        if (geminiSession) {
            // Initialize session lifecycle for local testing
            lifecycleManager.startSession(sessionId, connectionData, sessionConfig);
            console.log(`✅ [${sessionId}] Session lifecycle started for ${flowType} testing`);
            
            // Initialize Deepgram transcription for local testing
            try {
                const dgConnection = await deps.transcriptionManager.initializeLocalTranscription(sessionId, connectionData);
                if (dgConnection) {
                    connectionData.deepgramConnection = dgConnection;
                    console.log(`✅ [${sessionId}] Local Deepgram transcription initialized for ${flowType} testing`);
                }
            } catch (error) {
                console.warn(`⚠️ [${sessionId}] Failed to initialize local Deepgram transcription:`, error);
            }

            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-started',
                    sessionId: sessionId,
                    flowType: flowType,
                    scriptId: sessionConfig.scriptId,
                    config: {
                    voice: sessionConfig.voice,
                    model: sessionConfig.model,
                    isIncomingCall: isIncomingCall,
                    transcriptionEnabled: !!connectionData.deepgramConnection
                }
                }));
            }
            console.log(`✅ [${sessionId}] ${flowType.toUpperCase()} testing session started successfully`);
        } else {
            console.error(`❌ [${sessionId}] Gemini session creation returned null/undefined`);
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: 'Failed to create Gemini session'
                }));
            }
        }

        return { geminiSession, isSessionActive };

    } catch (startSessionError) {
        console.error(`❌ [${sessionId}] Critical error in start-session for ${flowType}:`, startSessionError);
        console.error(`❌ [${sessionId}] Error stack:`, (startSessionError as Error).stack);
        try {
            if (ws.readyState === 1) {
                ws.send(JSON.stringify({
                    type: 'session-error',
                    error: `Session start failed: ${(startSessionError as Error).message}`
                }));
            }
        } catch (sendError) {
            console.error(`❌ [${sessionId}] Failed to send error message:`, sendError);
        }
        return { geminiSession: null, isSessionActive: false };
    }
}

async function handleAudioData(
    sessionId: string, 
    data: LocalAudioMessage, 
    geminiSession: any, 
    isSessionActive: boolean, 
    deps: FlowDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    lifecycleManager: any, 
    recoveryManager: any, 
    flowType: string
): Promise<void> {
    if (geminiSession && isSessionActive && (data.audioData || data.audio)) {
        try {
            // Update activity for session persistence
            lifecycleManager.updateActivity(sessionId);

            // Handle browser audio data (support both audioData and audio fields)
            const base64Audio = data.audioData || data.audio || '';
            console.log(`🔍 [${sessionId}] About to call sendBrowserAudioToGemini - audioSize: ${base64Audio.length}, geminiSession exists: ${!!geminiSession}`);
            await deps.sessionManager.sendBrowserAudioToGemini(sessionId, geminiSession, base64Audio);

            // Send audio to Deepgram for transcription (local testing)
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && connectionData.deepgramConnection && deps.transcriptionManager) {
                try {
                    const audioBuffer = Buffer.from(base64Audio, 'base64');
                    deps.transcriptionManager.sendAudioToTranscription(sessionId, audioBuffer);
                } catch (dgError) {
                    console.warn(`⚠️ [${sessionId}] Local Deepgram send error:`, dgError);
                }
            }

        } catch (error) {
            console.error(`❌ [${sessionId}] Error processing ${flowType} testing audio:`, error);

            // Check if we need to recover the session
            const connectionData = activeConnections.get(sessionId);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(sessionId, activeConnections)) {
                console.log(`🔄 [${sessionId}] Audio processing failed in ${flowType} testing, attempting session recovery`);
                await recoveryManager.recoverSession(sessionId, 'audio_processing_error', activeConnections);
            }
        }
    }
}

async function handleTextMessage(
    sessionId: string, 
    data: LocalTextMessage, 
    geminiSession: any, 
    isSessionActive: boolean, 
    deps: FlowDependencies
): Promise<void> {
    if (geminiSession && isSessionActive && data.text) {
        try {
            console.log(`💬 [${sessionId}] Received text message: "${data.text}"`);
            await deps.sessionManager.sendTextToGemini(sessionId, geminiSession, data.text);
        } catch (error) {
            console.error(`❌ [${sessionId}] Error sending text to Gemini:`, error);
        }
    }
}

async function handleTurnComplete(
    sessionId: string, 
    geminiSession: any, 
    isSessionActive: boolean, 
    deps: FlowDependencies
): Promise<void> {
    // REMOVED: Turn complete handling for Live API
    // The Live API handles conversation turns automatically with Voice Activity Detection (VAD)
    // No manual turn management is needed - the model will respond when appropriate
    console.log(`🔚 [${sessionId}] Turn complete signal received - Live API handles this automatically`);
}

async function handleEndSession(
    sessionId: string, 
    deps: FlowDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    lifecycleManager: any
): Promise<void> {
    console.log(`🔚 [${sessionId}] Ending testing session - USER INITIATED`);
    // This is a user-initiated session end for testing
    const endConnectionData = activeConnections.get(sessionId);
    if (endConnectionData) {
        // Clean up Deepgram transcription
        if (endConnectionData.deepgramConnection) {
            deps.transcriptionManager.closeTranscription(sessionId);
        }

        if (lifecycleManager) {
            await lifecycleManager.requestSessionEnd(sessionId, endConnectionData, 'user_end_testing');
        } else {
            await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
        }
    } else {
        await endSession(sessionId, { ...deps, transcriptionManager: deps.transcriptionManager }, 'user_requested');
    }
}

async function handleRequestSummary(
    sessionId: string, 
    _deps: FlowDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    summaryManager: any, 
    contextManager: any
): Promise<void> {
    console.log(`📝 [${sessionId}] Manual summary requested for testing`);
    const summaryConnectionData = activeConnections.get(sessionId);
    if (summaryConnectionData && summaryManager) {
        await summaryManager.requestSummary(sessionId, summaryConnectionData, contextManager);
    }
}