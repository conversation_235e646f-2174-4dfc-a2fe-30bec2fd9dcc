import { endSession } from './session-utils.js';
import { websocketLogger } from '../utils/logger.js';
import { globalHeartbeatManager } from './heartbeat-manager.js';
import type {
    WebSocketConnection,
    FlowDependencies,
    ConnectionData,
    TwilioStartMessage,
    TwilioMediaMessage
} from '../types/websocket.js';
import type { WebSocket } from 'ws';

// Twilio flow handler for both inbound and outbound calls
export function handleTwilioFlow(connection: WebSocketConnection, deps: FlowDependencies): void {
    const {
        sessionManager,
        contextManager,
        activeConnections,
        healthMonitor,
        summaryManager,
        lifecycleManager,
        recoveryManager,
        transcriptionManager,
        flowType,
        getSessionConfig,
        isIncomingCall
    } = deps;

    const callSid = connection.query?.CallSid || `twilio-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    websocketLogger.info(`Twilio ${flowType} call started`, { callSid, flowType, isIncomingCall });

    const ws = (connection.socket || connection) as WebSocket;

    // CRITICAL FIX: Don.js't use persistent session variables - always get fresh state from activeConnections
    // This prevents stale state issues between multiple calls
    console.log(`🔄 [${callSid}] Starting fresh Twilio flow handler`);

    // Store event listeners for cleanup
    const eventListeners = new Map<string, Function>();

    const messageHandler = async (message: Buffer | string) => {
        try {
            const data = JSON.parse(message.toString());

            // CRITICAL FIX: Better validation of message structure
            if (!data || typeof data !== 'object') {
                console.log(`⚠️ [${callSid}] Invalid message structure - not an object:`, message);
                return;
            }

            // Twilio uses 'event' field for message type
            const messageType = data.event;

            if (!messageType) {
                console.log(`⚠️ [${callSid}] Message missing 'event' field:`, JSON.stringify(data, null, 2));
                return;
            }

            websocketLogger.debug(`Twilio ${flowType} message received`, { callSid, event: data.event, messageType });

            switch (messageType) {
                case 'connected':
                    // CRITICAL FIX: Handle 'connected' event that Twilio sends for some calls
                    console.log(`🔗 [${callSid}] Twilio CONNECTED event received:`, JSON.stringify(data, null, 2));
                    websocketLogger.info('Twilio connected event received', { callSid });
                    // This is just a connection confirmation, no action needed
                    // Update connection state to indicate Twilio WebSocket is ready
                    {
                        const connectionData = activeConnections.get(callSid);
                        if (connectionData) {
                            connectionData.twilioConnected = true;
                            console.log(`✅ [${callSid}] Twilio WebSocket connection confirmed`);
                        }
                    }
                    break;

                case 'start':
                    // Twilio sends 'start' event when stream begins
                    console.log(`🔍 [${callSid}] Twilio START event received:`, JSON.stringify(data, null, 2));
                    await handleTwilioStartSession(callSid, data as TwilioStartMessage, deps, ws, flowType, isIncomingCall, getSessionConfig, activeConnections, healthMonitor, lifecycleManager, sessionManager);
                    break;

                case 'media':
                    // Always get fresh session state from activeConnections
                    const connectionData = activeConnections.get(callSid);
                    const geminiSession = connectionData?.geminiSession;
                    const isSessionActive = connectionData?.isSessionActive;

                    await handleTwilioMedia(callSid, data as TwilioMediaMessage, geminiSession, isSessionActive, deps, activeConnections, lifecycleManager, recoveryManager);
                    break;

                case 'mark':
                    websocketLogger.debug('Mark received', { callSid });
                    break;

                case 'stop':
                    websocketLogger.info('Stop message received from Twilio', { callSid });
                    await handleTwilioEndSession(callSid, deps, activeConnections, lifecycleManager);
                    break;

                default:
                    websocketLogger.warn(`Unknown Twilio message event: ${messageType}`, { callSid });
                    console.log(`🔍 [${callSid}] Unknown message:`, JSON.stringify(data, null, 2));
                    console.log(`⚠️ [${callSid}] Received invalid message structure`);
            }
        } catch (error) {
            if (error instanceof SyntaxError) {
                console.log(`❌ [${callSid}] JSON parsing error for message:`, message);
                websocketLogger.error(`JSON parsing error for Twilio ${flowType} message`, error, { callSid });
            } else {
                websocketLogger.error(`Error processing Twilio ${flowType} message`, error, { callSid });
            }
        }
    };

    // Store handlers for cleanup
    eventListeners.set('message', messageHandler);
    ws.on('message', messageHandler);

    const closeHandler = async (code: number, reason: Buffer) => {
        websocketLogger.info(`Twilio ${flowType} connection closed`, {
            callSid,
            code,
            reason: reason ? reason.toString() : 'No reason',
            closeCode: code
        });

        // Stop heartbeat monitoring
        globalHeartbeatManager.stopHeartbeat(callSid);

        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // CRITICAL FIX: Don.js't end session immediately on WebSocket close
            // Twilio WebSocket can close/reconnect during normal call flow
            // Only end session if this is a deliberate call termination

            // Check if this is a normal close (1000) or abnormal close
            // Code 1005 means "no status received" and is common for Twilio WebSocket connections
            const isNormalClose = code === 1000 || code === 1005;
            const isCallStillActive = connectionData.isSessionActive && connectionData.geminiSession;

            // CRITICAL FIX: Don.js't end session on WebSocket close unless call is actually terminated
            // Twilio WebSocket can close while call is still active
            // Only end session if we received a .js'stop.js' message or call status indicates completion
            if (!isCallStillActive || connectionData.callCompleted || connectionData.stopReceived) {
                // Session already inactive or call explicitly ended - safe to end
                websocketLogger.info(`Ending session due to inactive session or explicit call termination`, {
                    callSid,
                    code,
                    isCallStillActive,
                    callCompleted: connectionData.callCompleted,
                    stopReceived: connectionData.stopReceived
                });
                if (lifecycleManager) {
                    await lifecycleManager.requestSessionEnd(callSid, connectionData, 'twilio_connection_closed');
                } else {
                    endSession(callSid, deps, 'twilio_connection_closed');
                }
            } else {
                // WebSocket closed but session still active - don.js't end session
                websocketLogger.warn(`WebSocket closed but session still active - keeping session alive`, {
                    callSid,
                    code,
                    isCallStillActive
                });
                connectionData.wsDisconnected = true;
                connectionData.lastDisconnectTime = Date.now();

                // Don.js't end session - let call status webhook handle actual call termination
            }
        }

        // Clean up event listeners
        for (const [event, handler] of eventListeners) {
            ws.removeListener(event, handler as any);
        }
        eventListeners.clear();

        // CRITICAL: Clean up connection data to prevent stale state
        console.log(`🧹 [${callSid}] Cleaning up connection data to prevent stale state`);
        activeConnections.delete(callSid);
    };

    const errorHandler = async (error: Error) => {
        websocketLogger.error(`Twilio ${flowType} error`, error, { callSid });

        const connectionData = activeConnections.get(callSid);
        if (connectionData) {
            // Try recovery first if possible
            if (recoveryManager && contextManager.canRecover(callSid)) {
                websocketLogger.info('Twilio WebSocket error detected, attempting session recovery', { callSid });
                contextManager.markSessionInterrupted(callSid, 'twilio_websocket_error');
                setTimeout(async () => {
                    await recoveryManager.recoverSession(callSid, 'twilio_websocket_error', activeConnections);
                }, 1000);
            } else {
                // Mark connection as errored but don't immediately end session
                // Let the call status webhook or explicit user action end the session
                websocketLogger.warn('WebSocket error but no recovery available - marking connection as errored', { callSid });
                connectionData.wsError = true;
                connectionData.lastErrorTime = Date.now();

                // Clean up Deepgram transcription
                if (connectionData.deepgramConnection) {
                    deps.transcriptionManager.closeTranscription(callSid);
                }

                // Only end session if this is a critical error and session is not active
                if (!connectionData.isSessionActive || !connectionData.geminiSession) {
                    endSession(callSid, { ...deps, transcriptionManager: deps.transcriptionManager }, 'twilio_connection_error');
                }
            }
        }
    };

    // Register all event listeners
    eventListeners.set('close', closeHandler);
    eventListeners.set('error', errorHandler);
    ws.on('close', closeHandler);
    ws.on('error', errorHandler);
}

async function handleTwilioStartSession(
    callSid: string, 
    data: TwilioStartMessage, 
    deps: FlowDependencies, 
    ws: WebSocket, 
    flowType: string, 
    isIncomingCall: boolean, 
    getSessionConfig: () => any, 
    activeConnections: Map<string, ConnectionData>, 
    healthMonitor: any, 
    lifecycleManager: any, 
    sessionManager: any
): Promise<void> {
    websocketLogger.info(`Starting Twilio ${flowType} session`, { callSid });

    try {
        // Get session config from the stored configuration (set during webhook call)
        let sessionConfig = getSessionConfig();

        // Extract Twilio stream information
        const streamSid = data.start?.streamSid;
        const accountSid = data.start?.accountSid;
        const twilioCallSid = data.start?.callSid;

        console.log(`🔍 [${callSid}] Twilio stream info:`, {
            streamSid,
            accountSid,
            twilioCallSid,
            configFound: !!sessionConfig,
            hasInstructions: !!sessionConfig?.aiInstructions
        });

        // Ensure we have a valid session config
        if (!sessionConfig || !sessionConfig.aiInstructions) {
            console.warn(`⚠️ [${callSid}] No session config found, using fallback`);
            // Try to get current script as fallback
            try {
                const currentScript = isIncomingCall
                    ? deps.scriptManager.getCurrentIncomingScript()
                    : deps.scriptManager.getCurrentOutboundScript();

                if (currentScript) {
                    sessionConfig = deps.scriptManager.getScriptConfig(currentScript.id, isIncomingCall);
                    console.log(`✅ [${callSid}] Using fallback script: ${currentScript.id}`);
                }
            } catch (error) {
                console.error(`❌ [${callSid}] Error getting fallback script:`, error);
            }
        }

        // Store enhanced connection data
        const connectionData: ConnectionData = {
            ws: ws, // Standardized WebSocket property name
            twilioWs: ws, // Also store as twilioWs for audio forwarding compatibility
            callSid,
            sessionId: callSid,
            streamSid: streamSid, // Add streamSid for audio forwarding
            sequenceNumber: 0, // Initialize sequence number for Twilio audio packets
            isSessionActive: false,
            summaryRequested: false,
            summaryReceived: false,
            summaryText: '',
            conversationLog: [],
            fullTranscript: [],
            speechTranscript: [],
            isIncomingCall,
            sessionType: 'twilio_call',
            flowType,
            sessionStartTime: Date.now(),
            lastActivity: Date.now(),
            targetName: sessionConfig.targetName || 'Contact',
            targetPhoneNumber: sessionConfig.targetPhoneNumber || '+1234567890',
            originalAIInstructions: sessionConfig.aiInstructions,
            scriptId: sessionConfig.scriptId,
            isTwilioCall: true,
            // Add missing properties to match testing flows
            lastAIResponse: Date.now(), // Track AI responsiveness
            responseTimeouts: 0, // Count consecutive timeouts
            connectionQuality: 'good', // Track connection quality
            lastContextSave: Date.now(), // For periodic context saving
            contextSaveInterval: null, // For periodic context saving
            // Audio forwarding properties
            audioForwardingEnabled: true, // Enable audio forwarding for Twilio calls
            lastAudioSent: 0 // Track last audio packet sent time
        };
        
        activeConnections.set(callSid, connectionData);

        // Track connection health
        healthMonitor.trackConnection(callSid, 'connected', {
            flowType,
            isTwilioCall: true,
            scriptId: sessionConfig.scriptId
        });

        // Create Gemini session
        const geminiSession = await sessionManager.createGeminiSession(callSid, sessionConfig, connectionData);

        if (geminiSession) {
            connectionData.geminiSession = geminiSession;
            // Note: isSessionActive will be set to true by the session manager.js's onopen callback
            console.log(`✅ [${callSid}] Gemini session created, waiting for activation...`);

            // Start lifecycle management for the session
            if (lifecycleManager) {
                lifecycleManager.startSession(callSid, connectionData, sessionConfig);
                console.log(`✅ [${callSid}] Lifecycle management started`);
            }

            // Start WebSocket heartbeat monitoring for Twilio
            // Note: Twilio WebSockets may not respond to standard pings, so use longer timeouts
            globalHeartbeatManager.startHeartbeat(
                callSid,
                ws,
                60000, // 60 second ping interval (longer for Twilio)
                30000, // 30 second pong timeout (longer for Twilio)
                (sessionId: string, ws: WebSocket) => {
                    websocketLogger.warn('Twilio WebSocket heartbeat timeout - connection may be stale', { callSid });
                    // Don't immediately end session on heartbeat timeout for Twilio
                    // Mark connection as potentially stale but let call status webhook handle cleanup
                    const connectionData = activeConnections.get(callSid);
                    if (connectionData) {
                        connectionData.heartbeatTimeout = true;
                        connectionData.lastHeartbeatTimeout = Date.now();
                        websocketLogger.info('Marked Twilio connection as having heartbeat timeout', { callSid });
                    }
                }
            );

            // Live API setup is now handled by SessionManager during session creation
            // This eliminates duplicate setup messages that could override instructions
            websocketLogger.info(`Live API setup handled by SessionManager for ${flowType}`, { callSid });

            // Log session configuration for debugging
            console.log(`🔍 [${callSid}] Session config for ${flowType}:`, {
                hasInstructions: !!sessionConfig.aiInstructions,
                instructionsLength: sessionConfig.aiInstructions?.length || 0,
                scriptId: sessionConfig.scriptId,
                scriptType: sessionConfig.scriptType,
                voice: sessionConfig.voice,
                model: sessionConfig.model,
                isIncomingCall: sessionConfig.isIncomingCall
            });

            ws.send(JSON.stringify({
                type: 'session-started',
                callSid,
                flowType,
                scriptId: sessionConfig.scriptId
            }));
            
            websocketLogger.info(`Twilio ${flowType} session started successfully`, { callSid });
        } else {
            // Gemini session failed to initialize
            websocketLogger.error(`Failed to create Gemini session for ${flowType} call`, { callSid });
            ws.send(JSON.stringify({
                type: 'session-error',
                error: 'Failed to initialize AI session. Please try again later.',
                critical: true
            }));
            
            // For Twilio calls, we should end the call gracefully
            if (deps.twilioHelper) {
                try {
                    await deps.twilioHelper.endCallWithMessage(callSid, 'We apologize, but we are unable to process your call at this time. Please try again later.');
                } catch (err) {
                    websocketLogger.error('Failed to end call with error message', err, { callSid });
                }
            }

            // Clean up and close connection
            endSession(callSid, deps, 'gemini_session_failed');
            ws.close();
            return;
        }

    } catch (error) {
        websocketLogger.error(`Error starting Twilio ${flowType} session`, error, { callSid });
        ws.send(JSON.stringify({
            type: 'session-error',
            error: `Session start failed: ${(error as Error).message}`
        }));
    }
}

async function handleTwilioMedia(
    callSid: string, 
    data: TwilioMediaMessage, 
    geminiSession: any, 
    isSessionActive: boolean, 
    deps: FlowDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    lifecycleManager: any, 
    recoveryManager: any
): Promise<void> {
    if (data.media && data.media.payload) {
        try {
            // Update activity for session persistence
            lifecycleManager.updateActivity(callSid);

            // Get fresh connection data to check current session state
            const connectionData = activeConnections.get(callSid);
            const currentIsSessionActive = connectionData?.isSessionActive || isSessionActive;
            const currentGeminiSession = connectionData?.geminiSession || geminiSession;

            // Convert Twilio μ-law audio to PCM
            const ulawAudio = Buffer.from(data.media.payload, 'base64');

            // Send to Gemini only if session is truly active
            if (deps.sessionManager && currentGeminiSession && currentIsSessionActive) {
                console.log(`🔍 [${callSid}] Sending audio to Gemini - session active: ${currentIsSessionActive}`);
                await deps.sessionManager.sendAudioToGemini(callSid, currentGeminiSession, ulawAudio);
            } else {
                console.log(`⚠️ [${callSid}] Skipping audio send - session not ready (active: ${currentIsSessionActive}, session: ${!!currentGeminiSession})`);
            }

        } catch (error) {
            websocketLogger.error(`Error processing Twilio media`, error, { callSid });
            
            // Attempt recovery if needed
            const connectionData = activeConnections.get(callSid);
            if (connectionData && recoveryManager && recoveryManager.needsRecovery(callSid, activeConnections)) {
                await recoveryManager.recoverSession(callSid, 'media_processing_error', activeConnections);
            }
        }
    }
}

async function handleTwilioEndSession(
    callSid: string, 
    deps: FlowDependencies, 
    activeConnections: Map<string, ConnectionData>, 
    lifecycleManager: any
): Promise<void> {
    websocketLogger.info('Ending Twilio session - stop received', { callSid });

    const connectionData = activeConnections.get(callSid);
    if (connectionData) {
        // Mark that stop was received to prevent premature session termination on WebSocket close
        connectionData.stopReceived = true;

        if (lifecycleManager) {
            await lifecycleManager.requestSessionEnd(callSid, connectionData, 'twilio_stop_received');
        } else {
            endSession(callSid, deps, 'twilio_stop_received');
        }
    }
}