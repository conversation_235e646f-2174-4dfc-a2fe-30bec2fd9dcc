export class ScriptCache<T = any> {
  private cache: Map<string, T>;
  private timestamps: Map<string, number>;
  private timeout: number;

  constructor(timeout: number = 300000) {
    this.cache = new Map();
    this.timestamps = new Map();
    this.timeout = timeout;
  }

  set(key: string, value: T): void {
    this.cache.set(key, value);
    this.timestamps.set(key, Date.now());
  }

  get(key: string): T | null {
    const ts = this.timestamps.get(key);
    if (!ts) return null;
    if (Date.now() - ts > this.timeout) {
      this.cache.delete(key);
      this.timestamps.delete(key);
      return null;
    }
    return this.cache.get(key) || null;
  }

  has(key: string): boolean {
    const value = this.get(key);
    return value !== null;
  }

  delete(key: string): boolean {
    this.timestamps.delete(key);
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
    this.timestamps.clear();
  }

  size(): number {
    return this.cache.size;
  }
}