// Management routes for voice and model management
import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { VoiceManager } from '../gemini/voice-manager.js';
import { ModelManager } from '../gemini/model-manager.js';

interface Dependencies {
    voiceManager: VoiceManager;
    modelManager: ModelManager;
    healthMonitor?: any;
    activeConnections: Map<string, any>;
    contextManager: any;
}

interface SetVoiceBody {
    voice: string;
}

interface SetModelBody {
    model: string;
}

interface SessionContextParams {
    callSid: string;
}

export function registerManagementRoutes(fastify: FastifyInstance, dependencies: Dependencies): void {
    const { voiceManager, modelManager, healthMonitor, activeConnections, contextManager } = dependencies;

    // === VOICE MANAGEMENT ENDPOINTS ===
    
    // Get available voices
    fastify.get('/api/voice/available', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const voiceConfig = voiceManager.getVoiceConfig();
            reply.send({
                success: true,
                ...voiceConfig,
                description: "Available Gemini Live API voices with characteristics and voice mappings"
            });
        } catch (error) {
            console.error('❌ Error getting available voices:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Set voice
    fastify.post<{ Body: SetVoiceBody }>('/api/voice/set', async (request, reply) => {
        try {
            const { voice } = request.body;
            
            if (!voice) {
                return reply.status(400).send({ error: 'Voice parameter is required' });
            }

            const validation = voiceManager.validateVoice(voice);
            if (!validation.isValid) {
                return reply.status(400).send({
                    error: validation.error,
                    availableVoices: Object.keys(voiceManager.getAvailableVoices()),
                    voiceMapping: Object.keys(voiceManager.getVoiceMapping()),
                    suggestion: validation.suggestion
                });
            }

            const previousVoice = voiceManager.getDefaultVoice();
            const success = voiceManager.setDefaultVoice(validation.voice!);

            if (success) {
                reply.send({
                    success: true,
                    previousVoice,
                    currentVoice: validation.voice,
                    voiceCharacteristics: validation.info,
                    mapped: validation.mapped || false,
                    originalVoice: validation.originalVoice || validation.voice,
                    message: `Voice successfully changed to ${validation.voice}`,
                    timestamp: new Date().toISOString()
                });
            } else {
                reply.status(500).send({
                    success: false,
                    error: 'Failed to set voice'
                });
            }
        } catch (error) {
            console.error('❌ Error setting voice:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // === MODEL MANAGEMENT ENDPOINTS ===
    
    // Get available models
    fastify.get('/api/model/available', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const modelConfig = modelManager.getModelConfig();
            reply.send({
                success: true,
                ...modelConfig,
                description: "Available Gemini models for Live API"
            });
        } catch (error) {
            console.error('❌ Error getting available models:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Set model
    fastify.post<{ Body: SetModelBody }>('/api/model/set', async (request, reply) => {
        try {
            const { model } = request.body;
            
            if (!model) {
                return reply.status(400).send({ error: 'Model parameter is required' });
            }

            const validation = modelManager.validateModel(model);
            if (!validation.isValid) {
                return reply.status(400).send({
                    error: validation.error,
                    availableModels: Object.keys(modelManager.getAvailableModels()),
                    suggestion: validation.suggestion
                });
            }

            const previousModel = modelManager.getCurrentModel();
            const success = modelManager.setCurrentModel(validation.model!);

            if (success) {
                reply.send({
                    success: true,
                    previousModel,
                    currentModel: validation.model,
                    modelInfo: validation.info,
                    message: `Model successfully changed to ${validation.model}`,
                    timestamp: new Date().toISOString()
                });
            } else {
                reply.status(500).send({
                    success: false,
                    error: 'Failed to set model'
                });
            }
        } catch (error) {
            console.error('❌ Error setting model:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Get model statistics
    fastify.get('/api/model/stats', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const stats = modelManager.getModelStats();
            reply.send({
                success: true,
                stats,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('❌ Error getting model stats:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // === ANALYTICS AND METRICS ENDPOINTS ===
    // Note: /api/connection-metrics is already defined in routes.js

    // Get script analytics (placeholder implementation)
    fastify.get('/api/incoming-scripts/analytics', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            // Placeholder analytics data - would be replaced with actual metrics
            const analytics = {
                totalCalls: 0,
                averageDuration: 0,
                successRate: 0.95,
                topScripts: [] as any[],
                callsByHour: {},
                resolutionRate: 0.85,
                escalationRate: 0.15,
                satisfactionScore: 4.2
            };

            reply.send({
                success: true,
                analytics,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('❌ Error getting script analytics:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Get call history (placeholder implementation)
    fastify.get<{ Querystring: { limit?: string } }>('/api/incoming-scripts/history', async (request, reply) => {
        try {
            const limit = parseInt(request.query.limit || '50');

            // Placeholder history data - would be replaced with actual call history
            const history: any[] = [];

            reply.send({
                success: true,
                history,
                limit,
                total: history.length,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('❌ Error getting call history:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Get incoming analytics (NEW SYSTEM)
    fastify.get('/api/incoming-analytics', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            // Placeholder analytics for incoming system
            const analytics = {
                totalIncomingCalls: 0,
                averageHandleTime: 0,
                firstCallResolution: 0.80,
                customerSatisfaction: 4.3,
                scenarioDistribution: {},
                peakHours: [] as string[],
                agentPerformance: {}
            };

            reply.send({
                success: true,
                analytics,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('❌ Error getting incoming analytics:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Get incoming call history (NEW SYSTEM)
    fastify.get<{ Querystring: { limit?: string } }>('/api/incoming-history', async (request, reply) => {
        try {
            const limit = parseInt(request.query.limit || '50');

            // Placeholder history for incoming calls
            const history: any[] = [];

            reply.send({
                success: true,
                history,
                limit,
                total: history.length,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('❌ Error getting incoming history:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Get active connections status
    fastify.get('/api/active-connections', async (request: FastifyRequest, reply: FastifyReply) => {
        try {
            const connections: any[] = [];

            if (activeConnections) {
                for (const [callSid, connectionData] of activeConnections.entries()) {
                    connections.push({
                        callSid,
                        isActive: connectionData.isSessionActive,
                        startTime: connectionData.startTime,
                        targetName: connectionData.targetName,
                        targetPhoneNumber: connectionData.targetPhoneNumber,
                        hasGeminiSession: !!connectionData.geminiSession,
                        hasTwilioConnection: !!connectionData.twilioWs,
                        hasTranscription: !!connectionData.deepgramConnection
                    });
                }
            }

            reply.send({
                success: true,
                connections,
                totalActive: connections.length,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('❌ Error getting active connections:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Get session context for debugging
    fastify.get<{ Params: SessionContextParams }>('/api/session-context/:callSid', async (request, reply) => {
        try {
            const { callSid } = request.params;

            if (!contextManager) {
                return reply.status(503).send({
                    success: false,
                    error: 'Context manager not available'
                });
            }

            const context = contextManager.getSessionContext(callSid);

            if (!context) {
                return reply.status(404).send({
                    success: false,
                    error: 'Session context not found'
                });
            }

            // Return sanitized context (remove sensitive data)
            reply.send({
                success: true,
                context: {
                    callSid: context.callSid,
                    timestamp: context.timestamp,
                    sessionConfig: {
                        voice: context.sessionConfig?.voice,
                        model: context.sessionConfig?.model,
                        isIncomingCall: context.sessionConfig?.isIncomingCall
                    },
                    conversationState: {
                        isSessionActive: context.conversationState?.isSessionActive,
                        summaryRequested: context.conversationState?.summaryRequested,
                        conversationLogLength: context.conversationState?.conversationLog?.length || 0
                    },
                    connectionState: context.connectionState,
                    recoveryInfo: context.recoveryInfo
                },
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            console.error('❌ Error getting session context:', error);
            reply.status(500).send({ success: false, error: (error as Error).message });
        }
    });

    // Note: Legacy endpoints (/available-voices, /set-voice, /available-models, /set-model)
    // are already implemented in routes.js to avoid duplication
}