// WebSocket Types

import { WebSocket } from 'ws';
import { SessionManager } from '../session/session-manager.js';
import { ContextManager } from '../session/context-manager.js';
import { SummaryManager } from '../session/summary-manager.js';
import { HealthMonitor } from '../session/health-monitor.js';
import { LifecycleManager } from '../session/lifecycle-manager.js';
import { RecoveryManager } from '../session/recovery-manager.js';
import { TranscriptionManager } from '../audio/transcription-manager.js';
import { ScriptManager } from '../scripts/script-manager.js';
import { VoiceManager } from '../gemini/voice-manager.js';
import { ModelManager } from '../gemini/model-manager.js';
import { GeminiClient } from '../gemini/client.js';

export interface WebSocketConnection {
  socket?: WebSocket;
  query?: Record<string, any>;
}

export interface ConnectionData {
  ws?: WebSocket;
  twilioWs?: WebSocket;
  localWs?: WebSocket;
  sessionId: string;
  callSid?: string;
  streamSid?: string;
  sequenceNumber?: number;
  isSessionActive: boolean;
  summaryRequested: boolean;
  summaryReceived: boolean;
  summaryText: string;
  conversationLog: any[];
  fullTranscript: any[];
  speechTranscript: any[];
  isIncomingCall: boolean;
  sessionType: 'local_test' | 'twilio_call';
  flowType: string;
  sessionStartTime: number;
  lastActivity: number;
  targetName: string;
  targetPhoneNumber: string;
  originalAIInstructions: string;
  scriptId: string;
  isTestMode?: boolean;
  isTwilioCall?: boolean;
  lastAIResponse: number;
  responseTimeouts: number;
  connectionQuality: string;
  lastContextSave: number;
  contextSaveInterval: NodeJS.Timeout | null;
  audioForwardingEnabled?: boolean;
  lastAudioSent?: number;
  geminiSession?: any;
  deepgramConnection?: any;
  twilioConnected?: boolean;
  wsDisconnected?: boolean;
  lastDisconnectTime?: number;
  wsError?: boolean;
  lastErrorTime?: number;
  callCompleted?: boolean;
  stopReceived?: boolean;
  heartbeatTimeout?: boolean;
  lastHeartbeatTimeout?: number;
}

export interface SessionConfig {
  aiInstructions: string;
  voice: string;
  model: string;
  targetName?: string | null;
  targetPhoneNumber?: string | null;
  scriptType: string;
  scriptId: string;
  isIncomingCall?: boolean;
  isTestMode?: boolean;
}

export interface WebSocketDependencies {
  sessionManager: SessionManager;
  contextManager: ContextManager;
  activeConnections: Map<string, ConnectionData>;
  healthMonitor: HealthMonitor;
  summaryManager: SummaryManager;
  lifecycleManager: LifecycleManager;
  recoveryManager: RecoveryManager;
  transcriptionManager: TranscriptionManager;
  scriptManager: ScriptManager;
  voiceManager: VoiceManager;
  modelManager: ModelManager;
  getNextCallConfig?: () => SessionConfig | null;
  twilioHelper?: any;
  GEMINI_DEFAULT_VOICE: string;
  GEMINI_DEFAULT_MODEL: string;
  SUMMARY_GENERATION_PROMPT?: string;
  config?: any;
  geminiClient?: GeminiClient;
}

export interface FlowDependencies extends WebSocketDependencies {
  flowType: string;
  getSessionConfig: () => SessionConfig;
  isIncomingCall: boolean;
  callType?: string;
  isTestMode?: boolean;
  getOutboundTestConfig?: (deps: WebSocketDependencies) => SessionConfig;
  getInboundTestConfig?: (deps: WebSocketDependencies) => SessionConfig;
}

export interface TwilioMediaMessage {
  event: 'media';
  media: {
    payload: string;
    chunk?: string;
    timestamp?: string;
    track?: string;
  };
  streamSid?: string;
  sequenceNumber?: string;
}

export interface TwilioStartMessage {
  event: 'start';
  start: {
    streamSid: string;
    accountSid: string;
    callSid: string;
    tracks?: string[];
    customParameters?: Record<string, any>;
  };
  streamSid?: string;
}

export interface LocalAudioMessage {
  type: 'audio-data' | 'audio';
  audio?: string;
  audioData?: string;
}

export interface LocalStartMessage {
  type: 'start-session';
  aiInstructions?: string;
  voice?: string;
  model?: string;
  scriptId?: string;
}

export interface LocalTextMessage {
  type: 'text-message';
  text: string;
}

export interface HeartbeatData {
  ws: WebSocket;
  interval: number;
  timeout: number;
  onTimeout: ((sessionId: string, ws: WebSocket) => void) | null;
  intervalId: NodeJS.Timeout | null;
  timeoutId: NodeJS.Timeout | null;
  lastPong: number;
  missedPings: number;
  pongHandler?: () => void;
}

export interface HeartbeatStatus {
  sessionId: string;
  lastPong: number;
  missedPings: number;
  interval: number;
  timeout: number;
  isActive: boolean;
}

export interface HeartbeatStatistics {
  totalSessions: number;
  healthySessions: number;
  unhealthySessions: number;
  averageLastPong: number;
}

export interface SessionData {
  geminiSession: any;
  isSessionActive: boolean;
}