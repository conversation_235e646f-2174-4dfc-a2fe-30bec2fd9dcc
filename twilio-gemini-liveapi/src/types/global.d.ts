// Global type definitions for the Twilio Gemini Live API project

import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { WebSocket } from 'ws';

// Configuration types
export interface ServerConfig {
  port: number;
  host: string;
  publicUrl: string;
  environment: 'development' | 'production' | 'test';
}

export interface AuthConfig {
  twilio: {
    accountSid: string;
    authToken: string;
  };
  gemini: {
    apiKey: string;
  };
  deepgram: {
    apiKey: string;
  };
  supabase: {
    url: string;
    anonKey: string;
  };
}

export interface AIConfig {
  gemini: {
    defaultModel: string;
    defaultVoice: string;
    maxTokens: number;
    temperature: number;
  };
}

export interface PromptsConfig {
  summaryGeneration: string;
  aiPrepareOutbound: string;
  incomingCallGreeting: string;
}

export interface AppConfig {
  environment: {
    nodeEnv: string;
    isDevelopment: boolean;
    isProduction: boolean;
    debugLevel: string;
    enableDetailedLogging: boolean;
  };
  server: ServerConfig & {
    corsOrigin: string;
    frontendUrl: string;
  };
  auth: AuthConfig & {
    openai: {
      apiKey?: string;
      apiUrl: string;
    };
    ngrok: {
      authToken?: string;
    };
  };
  twilio: {
    phoneNumbers: {
      default?: string;
      us?: string;
      cz?: string;
      es?: string;
    };
    defaultPhoneNumber?: string;
    webhooks: {
      voiceUrl?: string;
      statusCallbackUrl?: string;
      recordingStatusCallbackUrl?: string;
    };
  };
  ai: AIConfig & {
    openai: {
      model: string;
      chatModel: string;
      voice: string;
      temperature: number;
    };
  };
  audio: {
    inputFormat: string;
    outputFormat: string;
    sampleRate: number;
    twilioSampleRate: number;
    greetingAudioUrl?: string;
  };
  websocket: {
    protocol: string;
    url?: string;
  };
  transcription: {
    enabled: boolean;
    model: string;
    language: string;
    responseFormat: string;
  };
  campaigns: {
    scriptsPath: string;
    totalCampaigns: number;
    defaultCampaignId: number;
    enableCustomScripts: boolean;
    scriptCacheTimeout: number;
  };
  localization: {
    defaultLanguage: string;
    supportedLanguages: string[];
    enableMultiLanguage: boolean;
    fallbackLanguage: string;
  };
  voices: {
    defaultVoiceMapping: {
      [language: string]: {
        incoming: string;
        outbound: string;
      };
    };
    enableVoiceSelection: boolean;
  };
  business: {
    callTimeouts: {
      default: number;
      intro: number;
      response: number;
    };
    validation: {
      maxVehicles: number;
      maxClaims: number;
      minVehicleYear: number;
      maxVehicleYear: number;
    };
    transfer: {
      defaultTransferNumber: string;
      defaultAgentName: string;
      transferTimeout: number;
    };
  };
  prompts: PromptsConfig & {
    aiPrepareIncoming: string;
    systemMessageBio: string;
    systemMessageVoicePersonality: string;
    systemMessageVoiceSpeed: string;
  };
  security: {
    vocabularyRestrictions: string[];
    enableRecordingConfirmation: boolean;
    recordingConfirmationMessage: string;
  };
  performance: {
    enableCaching: boolean;
    cacheTimeout: number;
    maxConcurrentCalls: number;
    enableMetrics: boolean;
  };
}

// Session types
export interface SessionData {
  sessionId: string;
  callSid?: string;
  phoneNumber?: string;
  mode: 'outbound' | 'inbound' | 'outbound-testing' | 'inbound-testing';
  status: 'active' | 'ended' | 'error';
  startTime: Date;
  endTime?: Date;
  metadata?: Record<string, any>;
}

export interface ConnectionData {
  sessionId: string;
  twilioWs?: WebSocket;
  geminiWs?: WebSocket;
  mode: string;
  phoneNumber?: string;
  callSid?: string;
  isActive: boolean;
  startTime: Date;
  lastActivity: Date;
  metadata?: Record<string, any>;
}

// Audio types
export interface AudioChunk {
  data: Buffer;
  timestamp: number;
  sequenceNumber?: number;
}

export interface AudioConfig {
  sampleRate: number;
  channels: number;
  bitDepth: number;
  encoding: string;
}

// WebSocket message types
export interface WebSocketMessage {
  type: string;
  data?: any;
  sessionId?: string;
  timestamp?: number;
}

export interface TwilioMediaMessage {
  event: 'media' | 'start' | 'stop' | 'connected';
  sequenceNumber?: string;
  media?: {
    track: string;
    chunk: string;
    timestamp: string;
  };
  start?: {
    streamSid: string;
    accountSid: string;
    callSid: string;
    tracks: string[];
    mediaFormat: {
      encoding: string;
      sampleRate: number;
      channels: number;
    };
  };
  stop?: {
    accountSid: string;
    callSid: string;
  };
}

// Gemini API types
export interface GeminiMessage {
  role: 'user' | 'model';
  parts: Array<{
    text?: string;
    inlineData?: {
      mimeType: string;
      data: string;
    };
  }>;
}

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string;
      }>;
      role: string;
    };
    finishReason: string;
    index: number;
  }>;
}

// Script and campaign types
export interface CampaignScript {
  id: string;
  name: string;
  content: string;
  language: string;
  voice?: string;
  model?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CallConfig {
  phoneNumber: string;
  script?: CampaignScript;
  voice?: string;
  model?: string;
  mode: 'outbound' | 'inbound' | 'outbound-testing' | 'inbound-testing';
  metadata?: Record<string, any>;
}

// Manager interfaces
export interface SessionManager {
  createSession(sessionId: string, connectionData: ConnectionData): Promise<void>;
  getSession(sessionId: string): SessionData | undefined;
  updateSession(sessionId: string, updates: Partial<SessionData>): Promise<void>;
  endSession(sessionId: string): Promise<void>;
  cleanupSession(sessionId: string): void;
  getActiveSessions(): SessionData[];
}

export interface ContextManager {
  setContext(sessionId: string, context: any): void;
  getContext(sessionId: string): any;
  updateContext(sessionId: string, updates: any): void;
  clearContext(sessionId: string): void;
  clearAllContexts(): void;
  cleanupOldContexts(): void;
}

// Fastify extensions
declare module 'fastify' {
  interface FastifyInstance {
    getNextCallConfig?: () => Promise<CallConfig | null>;
  }
}

// Environment variables
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV: 'development' | 'production' | 'test';
      PORT: string;
      PUBLIC_URL: string;
      TWILIO_ACCOUNT_SID: string;
      TWILIO_AUTH_TOKEN: string;
      GEMINI_API_KEY: string;
      DEEPGRAM_API_KEY: string;
      SUPABASE_URL: string;
      SUPABASE_ANON_KEY: string;
    }
  }
}

export {};
