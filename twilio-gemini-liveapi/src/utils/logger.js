/**
 * Structured Logger for Twilio Gemini Live API
 * 
 * Provides consistent logging across all components with:
 * - CallSid/SessionId correlation
 * - Component-based categorization
 * - Performance timing
 * - Audio quality metrics
 * - Production-ready JSON output
 */

import { performance } from 'perf_hooks';

/**
 * Log levels in order of severity
 */
export const LogLevel = {
    DEBUG: 0,
    INFO: 1,
    WARN: 2,
    ERROR: 3
};

/**
 * Component identifiers for structured logging
 */
export const Component = {
    GEMINI: 'GEMINI',
    TWILIO: 'TWILIO', 
    AUDIO: 'AUDIO',
    SESSION: 'SESSION',
    API: 'API',
    CONFIG: 'CONFIG',
    WEBSOCKET: 'WEBSOCKET',
    RECOVERY: 'RECOVERY',
    HEALTH: 'HEALTH',
    AUTH: 'AUTH',
    DEEPGRAM: 'DEEPGRAM'
};

/**
 * Performance timer for measuring operation duration
 */
class PerformanceTimer {
    constructor(operation, logger, callSid = null, component = null) {
        this.operation = operation;
        this.logger = logger;
        this.callSid = callSid;
        this.component = component;
        this.startTime = performance.now();
    }

    end(additionalData = {}) {
        const duration = Math.round(performance.now() - this.startTime);
        this.logger.debug(`${this.operation} completed`, {
            ...additionalData,
            duration_ms: duration,
            operation: this.operation
        }, this.callSid, this.component);
        return duration;
    }
}

/**
 * Main Logger class
 */
export class Logger {
    constructor(options = {}) {
        this.level = options.level || (process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG);
        this.enableJson = options.json || process.env.NODE_ENV === 'production';
        this.enableColors = options.colors !== false && process.env.NODE_ENV !== 'production';
        this.component = options.component || null;
        
        // Performance tracking
        this.performanceMetrics = new Map();
        this.audioMetrics = new Map();
        
        // Emoji mappings for console output
        this.levelEmojis = {
            [LogLevel.DEBUG]: '🔍',
            [LogLevel.INFO]: 'ℹ️',
            [LogLevel.WARN]: '⚠️',
            [LogLevel.ERROR]: '❌'
        };

        this.componentEmojis = {
            [Component.GEMINI]: '🤖',
            [Component.TWILIO]: '📞',
            [Component.AUDIO]: '🎤',
            [Component.SESSION]: '🔄',
            [Component.API]: '🌐',
            [Component.CONFIG]: '⚙️',
            [Component.WEBSOCKET]: '🔌',
            [Component.RECOVERY]: '🚑',
            [Component.HEALTH]: '🏥',
            [Component.AUTH]: '🔐',
            [Component.DEEPGRAM]: '📝'
        };
    }

    /**
     * Create a child logger with a specific component
     */
    child(component) {
        return new Logger({
            level: this.level,
            json: this.enableJson,
            colors: this.enableColors,
            component
        });
    }

    /**
     * Format log message for output
     */
    formatMessage(level, message, data = {}, callSid = null, component = null) {
        const timestamp = new Date().toISOString();
        const logComponent = component || this.component;
        const logLevel = Object.keys(LogLevel)[level];

        const logEntry = {
            timestamp,
            level: logLevel,
            message,
            component: logComponent,
            callSid,
            ...data
        };

        if (this.enableJson) {
            return JSON.stringify(logEntry);
        }

        // Console formatting with emojis and colors
        const levelEmoji = this.levelEmojis[level] || '';
        const componentEmoji = logComponent ? this.componentEmojis[logComponent] || '' : '';
        const callSidStr = callSid ? `[${callSid}]` : '';
        const componentStr = logComponent ? `[${logComponent}]` : '';
        
        let formattedMessage = `${timestamp} ${levelEmoji} ${callSidStr} ${componentEmoji}${componentStr} ${message}`;
        
        if (Object.keys(data).length > 0) {
            formattedMessage += ` | ${JSON.stringify(data)}`;
        }

        return formattedMessage;
    }

    /**
     * Log at specified level
     */
    log(level, message, data = {}, callSid = null, component = null) {
        if (level < this.level) {
            return;
        }

        const formatted = this.formatMessage(level, message, data, callSid, component);
        
        switch (level) {
            case LogLevel.ERROR:
                console.error(formatted);
                break;
            case LogLevel.WARN:
                console.warn(formatted);
                break;
            case LogLevel.INFO:
                console.info(formatted);
                break;
            case LogLevel.DEBUG:
            default:
                console.log(formatted);
                break;
        }
    }

    /**
     * Debug level logging
     */
    debug(message, data = {}, callSid = null, component = null) {
        this.log(LogLevel.DEBUG, message, data, callSid, component);
    }

    /**
     * Info level logging
     */
    info(message, data = {}, callSid = null, component = null) {
        this.log(LogLevel.INFO, message, data, callSid, component);
    }

    /**
     * Warning level logging
     */
    warn(message, data = {}, callSid = null, component = null) {
        this.log(LogLevel.WARN, message, data, callSid, component);
    }

    /**
     * Error level logging
     */
    error(message, data = {}, callSid = null, component = null) {
        // Include stack trace for errors if available
        if (data instanceof Error) {
            data = {
                error: data.message,
                stack: data.stack,
                name: data.name
            };
        } else if (data.error instanceof Error) {
            data.error = {
                message: data.error.message,
                stack: data.error.stack,
                name: data.error.name
            };
        }
        
        this.log(LogLevel.ERROR, message, data, callSid, component);
    }

    /**
     * Start performance timing
     */
    startTimer(operation, callSid = null, component = null) {
        return new PerformanceTimer(operation, this, callSid, component);
    }

    /**
     * Log API call performance
     */
    logApiCall(method, url, statusCode, duration, callSid = null, component = Component.API) {
        const level = statusCode >= 400 ? LogLevel.WARN : LogLevel.INFO;
        this.log(level, `API ${method} ${url}`, {
            method,
            url,
            statusCode,
            duration_ms: duration,
            type: 'api_call'
        }, callSid, component);
    }

    /**
     * Set log level dynamically
     */
    setLevel(level) {
        this.level = level;
        this.info('Log level changed', { level: Object.keys(LogLevel)[level] });
    }

    /**
     * Get current log level
     */
    getLevel() {
        return this.level;
    }

    /**
     * Check if a level would be logged
     */
    isLevelEnabled(level) {
        return level >= this.level;
    }
}

// Create default logger instance
export const logger = new Logger();

// Create component-specific loggers
export const geminiLogger = logger.child(Component.GEMINI);
export const twilioLogger = logger.child(Component.TWILIO);
export const audioLogger = logger.child(Component.AUDIO);
export const sessionLogger = logger.child(Component.SESSION);
export const apiLogger = logger.child(Component.API);
export const configLogger = logger.child(Component.CONFIG);
export const websocketLogger = logger.child(Component.WEBSOCKET);
export const recoveryLogger = logger.child(Component.RECOVERY);
export const healthLogger = logger.child(Component.HEALTH);
export const authLogger = logger.child(Component.AUTH);

/**
 * Global logging control utilities
 */
export const LoggingControl = {
    /**
     * Set log level for all loggers
     */
    setGlobalLevel(level) {
        logger.setLevel(level);
        geminiLogger.setLevel(level);
        twilioLogger.setLevel(level);
        audioLogger.setLevel(level);
        sessionLogger.setLevel(level);
        apiLogger.setLevel(level);
        configLogger.setLevel(level);
        websocketLogger.setLevel(level);
        recoveryLogger.setLevel(level);
        healthLogger.setLevel(level);
        authLogger.setLevel(level);

        logger.info('Global log level changed', {
            level: Object.keys(LogLevel)[level],
            affectedLoggers: 11
        });
    },

    /**
     * Set production mode (INFO level, JSON output)
     */
    setProductionMode() {
        this.setGlobalLevel(LogLevel.INFO);
        logger.info('Switched to production logging mode');
    },

    /**
     * Set development mode (DEBUG level, console output)
     */
    setDevelopmentMode() {
        this.setGlobalLevel(LogLevel.DEBUG);
        logger.info('Switched to development logging mode');
    },

    /**
     * Set quiet mode (ERROR level only)
     */
    setQuietMode() {
        this.setGlobalLevel(LogLevel.ERROR);
        logger.error('Switched to quiet logging mode - only errors will be shown');
    },

    /**
     * Get current global log level
     */
    getGlobalLevel() {
        return logger.getLevel();
    }
};

// Export utilities
export { PerformanceTimer };
