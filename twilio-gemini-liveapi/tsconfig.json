{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "outDir": "./dist", "rootDir": "./", "strict": true, "declaration": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "incremental": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/config/*": ["./src/config/*"], "@/utils/*": ["./src/utils/*"], "@/gemini/*": ["./src/gemini/*"], "@/session/*": ["./src/session/*"], "@/audio/*": ["./src/audio/*"], "@/websocket/*": ["./src/websocket/*"], "@/api/*": ["./src/api/*"], "@/middleware/*": ["./src/middleware/*"], "@/scripts/*": ["./src/scripts/*"]}, "types": ["node"], "lib": ["ES2022", "DOM"]}, "include": ["src/**/*", "index.ts", "*.ts", "*.js"], "exclude": ["node_modules", "dist", "test/**/*", "**/*.test.ts", "**/*.test.js"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}